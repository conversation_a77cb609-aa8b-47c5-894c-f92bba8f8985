<template>
  <div>
    <!-- 查看项目报价弹框 -->
    <el-dialog :title="'[查看]项目报价 - ' + quotationData.projectName" :model-value="dialogVisible" @update:model-value="dialogVisible = $event" width="80%" append-to-body>
      <!-- 项目基本信息 -->
      <el-descriptions title="项目基本信息" :column="3" border>
        <el-descriptions-item label="项目名称">{{ quotationData.projectName }}</el-descriptions-item>
        <el-descriptions-item label="项目编号">{{ quotationData.projectCode }}</el-descriptions-item>
        <el-descriptions-item label="合同编号">{{ quotationData.contractCode }}</el-descriptions-item>
        <el-descriptions-item label="服务类型">{{ quotationData.serviceType }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ quotationData.customerName }}</el-descriptions-item>
        <el-descriptions-item label="受检方企业名称">{{ quotationData.inspectedParty }}</el-descriptions-item>
        <el-descriptions-item label="项目负责人">{{ quotationData.projectManager }}</el-descriptions-item>
        <el-descriptions-item label="市场负责人">{{ quotationData.marketManager }}</el-descriptions-item>
        <el-descriptions-item label="项目技术人">{{ quotationData.technicalManager }}</el-descriptions-item>
        <el-descriptions-item label="委托日期">{{ quotationData.commissionDate }}</el-descriptions-item>
        <el-descriptions-item label="审核人">{{ getApproverNames(quotationData.approverList) }}</el-descriptions-item>
        <el-descriptions-item label="项目状态">{{ getStatusLabel(quotationData.status) }}</el-descriptions-item>
      </el-descriptions>

      <el-divider />

      <el-collapse v-model="activeNames">
        <!-- 检测项目费用明细 -->
        <el-collapse-item title="费用项1：检测项目费用明细" name="1">
          <el-table :data="feeDetailTableData" style="width: 100%" border>
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="样品类别" width="120">
              <template #default="scope">
                <el-tooltip :content="scope.row.category" placement="top" :disabled="!scope.row.category">
                  <div class="cell-content">{{ scope.row.category || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="点位名称" width="120">
              <template #default="scope">
                <el-tooltip :content="scope.row.pointName" placement="top" :disabled="!scope.row.pointName">
                  <div class="cell-content">{{ scope.row.pointName || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="参数" width="200">
              <template #default="scope">
                <el-tooltip :content="scope.row.parameters" placement="top" :disabled="!scope.row.parameters">
                  <div class="cell-content">{{ scope.row.parameters || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="参数个数" width="80" align="center">
              <template #default="scope">
                <span>{{ scope.row.parameterCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="检测点位" width="80" align="center">
              <template #default="scope">
                <span>{{ scope.row.pointCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="检测周期" width="80" align="center">
              <template #default="scope">
                <span>{{ scope.row.cycleCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="检测频率" width="80" align="center">
              <template #default="scope">
                <span>{{ scope.row.frequency || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="样品数" width="80" align="center">
              <template #default="scope">
                <span>{{ scope.row.sampleCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="采样单价" width="100" align="right">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.samplingUnitPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="采样费用" width="100" align="right">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.samplingFee) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="检测首项单价" width="120" align="right">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.firstItemPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="检测增项单价" width="120" align="right">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.additionalItemPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="检测封顶单价" width="120" align="right">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.testingFeeLimit) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="检测单价" width="100" align="right">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.testingUnitPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="检测总费用" width="120" align="right">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.testingFee) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="前处理单价" width="120" align="right">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.pretreatmentUnitPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="前处理费" width="100" align="right">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.pretreatmentFee) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="总价（元）" width="120" align="right">
              <template #default="scope">
                <span class="total-price">{{ formatCurrency(scope.row.itemTotalFee) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" width="150">
              <template #default="scope">
                <el-tooltip :content="scope.row.remark" placement="top" :disabled="!scope.row.remark">
                  <div class="cell-content">{{ scope.row.remark || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <!-- 费用汇总 -->
          <div class="fee-summary-section">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="fee-summary-item">
                  <span class="fee-label">采样费用合计：</span>
                  <span class="fee-value">{{ formatCurrency(feeCalculationData.samplingFee) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="fee-summary-item">
                  <span class="fee-label">检测费用合计：</span>
                  <span class="fee-value">{{ formatCurrency(feeCalculationData.testingFee) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="fee-summary-item">
                  <span class="fee-label">前处理费用合计：</span>
                  <span class="fee-value">{{ formatCurrency(feeCalculationData.pretreatmentFee) }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row style="margin-top: 10px;">
              <el-col :span="24">
                <div class="fee-summary-item total-fee">
                  <span class="fee-label">检测项目费用合计：</span>
                  <span class="fee-value total-amount">{{ formatCurrency(feeCalculationData.totalFee) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>

        <!-- 项目其他费用表 -->
        <el-collapse-item title="费用项2：项目其他费用表" name="2">
          <el-table :data="quotationData.otherFees" style="width: 100%" border>
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="费用名称" width="150">
              <template #default="scope">
                <el-tooltip :content="scope.row.feeName" placement="top" :disabled="!scope.row.feeName">
                  <div class="cell-content">{{ scope.row.feeName || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="数量" width="100">
              <template #default="scope">
                <span>{{ scope.row.quantity || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="单价" width="100">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.unitPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="总价" width="100">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.totalPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注">
              <template #default="scope">
                <el-tooltip :content="scope.row.remark" placement="top" :disabled="!scope.row.remark">
                  <div class="cell-content">{{ scope.row.remark || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <div class="fee-summary">
            <span>其他费用合计：{{ getOtherFeeTotal() }}</span>
          </div>
        </el-collapse-item>

        <!-- 项目总费用表 -->
        <el-collapse-item title="费用项3：项目总费用表" name="3">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="项目编号">{{ quotationData.projectCode }}</el-descriptions-item>
            <el-descriptions-item label="检测项目总折扣率">{{ totalFeeData.discountRate }}%</el-descriptions-item>
            <el-descriptions-item label="检测折后费用">
              <span class="fee-value">{{ formatCurrency(totalFeeData.discountedTestingFee) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="其他费用">
              <span class="fee-value">{{ formatCurrency(totalFeeData.otherFee) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="优惠前总费用">
              <span class="fee-value">{{ formatCurrency(totalFeeData.totalFeeBeforeDiscount) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="税率">{{ totalFeeData.taxRate }}%</el-descriptions-item>
            <el-descriptions-item label="税费">
              <span class="fee-value">{{ formatCurrency(totalFeeData.tax) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="优惠前总费用(税后)">
              <span class="fee-value">{{ formatCurrency(totalFeeData.totalFeeAfterTax) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="整体调整金额">
              <span class="fee-value" :class="{'negative-value': isNegative(totalFeeData.adjustmentAmount)}">
                {{ formatCurrency(totalFeeData.adjustmentAmount) }}
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="优惠后总金额">
              <span class="final-amount">{{ formatCurrency(totalFeeData.finalAmount) }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
      </el-collapse>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">关 闭</el-button>
          <el-button type="primary" @click="handleEdit">编辑报价</el-button>
          <el-button type="success" @click="handleModify">修改项目</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改项目信息弹框 -->
    <EditProjectInfoDialog
      :visible="editProjectInfoVisible"
      @update:visible="editProjectInfoVisible = $event"
      :quotation-id="props.quotationId"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { getProjectQuotation, getProjectQuotationFeeCalculation } from "@/api/quotation/projectQuotation"
import EditProjectInfoDialog from './EditProjectInfoDialog.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  quotationId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'edit', 'refresh'])

// 对话框可见性
const dialogVisible = ref(false)
// 修改项目信息弹框可见性
const editProjectInfoVisible = ref(false)

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.quotationId) {
    // 重置表单数据
    resetData()
    // 获取项目报价详情
    getQuotationDetail(props.quotationId)
  }
})

// 重置所有数据
function resetData() {
  quotationData.value = {
    id: undefined,
    projectName: '',
    projectCode: '',
    contractCode: '',
    serviceType: '',
    customerName: '',
    inspectedParty: '',
    projectManager: '',
    marketManager: '',
    technicalManager: '',
    commissionDate: '',
    status: '',
    items: [],
    otherFees: [],
    attachments: []
  }

  totalFeeData.value = {
    discountRate: 0,
    discountedTestingFee: 0,
    otherFee: 0,
    totalFeeBeforeDiscount: 0,
    taxRate: 0,
    tax: 0,
    totalFeeAfterTax: 0,
    adjustmentAmount: 0,
    finalAmount: 0
  }
}

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 折叠面板激活的项
const activeNames = ref(['1', '2', '3'])
// 报价数据对象
const quotationData = ref({
  id: undefined,
  projectName: '',
  projectCode: '',
  items: [],
  otherFees: []
})
// 总费用数据
const totalFeeData = ref({
  discountRate: 0,
  discountedTestingFee: 0,
  otherFee: 0,
  totalFeeBeforeDiscount: 0,
  taxRate: 0,
  tax: 0,
  totalFeeAfterTax: 0,
  adjustmentAmount: 0,
  finalAmount: 0
})

// 费用计算详情数据
const feeCalculationData = ref({
  samplingFee: 0,
  testingFee: 0,
  pretreatmentFee: 0,
  totalFee: 0,
  calculationDetails: {
    details: [],
    summary: {}
  }
})

// 费用明细表格数据
const feeDetailTableData = ref([])

// 获取其他费用合计
function getOtherFeeTotal() {
  let total = 0
  quotationData.value.otherFees.forEach(fee => {
    total += fee.totalPrice || 0
  })
  return total.toFixed(2)
}

// 获取状态标签
function getStatusLabel(status) {
  const statusMap = {
    '0': '草稿',
    '1': '待审核',
    '2': '已审核',
    '3': '已撤回',
    '4': '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 获取审批人名称
function getApproverNames(approverList) {
  if (!approverList || !Array.isArray(approverList)) {
    return ''
  }
  return approverList.map(approver => approver.nickName || approver.userName || '未知').join('、')
}

// 格式化货币
function formatCurrency(value) {
  if (value === null || value === undefined) return '0.00';
  return parseFloat(value).toFixed(2);
}

// 判断是否为负值
function isNegative(value) {
  return parseFloat(value) < 0;
}

// 获取项目报价详情
function getQuotationDetail(id) {
  // 获取基本报价信息
  getProjectQuotation(id).then(response => {
    quotationData.value = response.data

    // 如果没有其他费用数据，初始化一个空数组
    if (!quotationData.value.otherFees) {
      quotationData.value.otherFees = []
    }

    // 如果有总费用数据，则使用后端数据
    if (quotationData.value.totalFee) {
      totalFeeData.value = quotationData.value.totalFee
    }
  })

  // 获取费用计算详情
  getProjectQuotationFeeCalculation(id).then(response => {
    feeCalculationData.value = response.data

    // 处理费用明细表格数据
    if (response.data.calculationDetails && response.data.calculationDetails.details) {
      feeDetailTableData.value = processFeeDetailData(response.data.calculationDetails.details)
    }
  }).catch(error => {
    console.error('获取费用计算详情失败:', error)
    // 如果获取费用计算失败，使用空数据
    feeCalculationData.value = {
      samplingFee: 0,
      testingFee: 0,
      pretreatmentFee: 0,
      totalFee: 0,
      calculationDetails: { details: [], summary: {} }
    }
    feeDetailTableData.value = []
  })
}

// 处理费用明细数据
function processFeeDetailData(details) {
  // 按类别-方法-点位名称分组
  const groupedData = {}

  details.forEach(item => {
    const key = `${item.category}_${item.method}_${item.pointName || '未指定点位'}`

    if (!groupedData[key]) {
      groupedData[key] = {
        category: item.category,
        pointName: item.pointName || '未指定点位',
        parameters: [],
        parameterCount: 0,
        pointCount: item.pointCount,
        cycleCount: item.cycleCount,
        frequency: item.frequency,
        sampleCount: item.sampleCount,
        samplingUnitPrice: item.samplingUnitPrice,
        samplingFee: 0,
        firstItemPrice: item.firstItemPrice || 0,
        additionalItemPrice: item.additionalItemPrice || 0,
        testingFeeLimit: item.testingFeeLimit || 0,
        testingUnitPrice: item.testingUnitPrice,
        testingFee: 0,
        pretreatmentUnitPrice: item.pretreatmentUnitPrice,
        pretreatmentFee: 0,
        itemTotalFee: 0,
        remark: item.remark || ''
      }
    }

    // 收集参数
    if (item.parameter && !groupedData[key].parameters.includes(item.parameter)) {
      groupedData[key].parameters.push(item.parameter)
    }

    // 累加费用
    groupedData[key].samplingFee += item.samplingFee || 0
    groupedData[key].testingFee += item.testingFee || 0
    groupedData[key].pretreatmentFee += item.pretreatmentFee || 0
    groupedData[key].itemTotalFee += item.itemTotalFee || 0
  })

  // 转换为数组并处理参数显示
  return Object.values(groupedData).map(group => ({
    ...group,
    parameters: group.parameters.join('、'),
    parameterCount: group.parameters.length
  }))
}

// 关闭对话框
function closeDialog() {
  dialogVisible.value = false
}

// 编辑按钮点击事件
function handleEdit() {
  emit('edit', quotationData.value.id)
  closeDialog()
}

// 修改按钮点击事件
function handleModify() {
  console.log('修改按钮点击，当前 editProjectInfoVisible:', editProjectInfoVisible.value)
  editProjectInfoVisible.value = true
  console.log('修改按钮点击后，editProjectInfoVisible 设置为:', editProjectInfoVisible.value)
}

// 刷新数据
function handleRefresh() {
  getQuotationDetail(props.quotationId)
  emit('refresh')
}
</script>

<style scoped>
.fee-summary {
  margin-top: 10px;
  text-align: right;
  font-weight: bold;
  font-size: 16px;
}

.fee-summary-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.fee-summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.fee-summary-item.total-fee {
  border-top: 2px solid #409eff;
  margin-top: 10px;
  padding-top: 15px;
  font-size: 16px;
  font-weight: bold;
}

.fee-label {
  font-weight: 500;
  color: #606266;
}

.fee-value {
  font-weight: bold;
  color: #409eff;
  font-size: 14px;
}

.total-amount {
  font-size: 18px;
  color: #f56c6c;
}

.cell-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.el-table .cell {
  padding: 0 8px;
}

.total-price {
  font-weight: bold;
  color: #f56c6c;
}

.negative-value {
  color: #67c23a;
}

.final-amount {
  font-weight: bold;
  color: #f56c6c;
  font-size: 16px;
}

/* 表格样式优化 */
.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.el-table td {
  padding: 8px 0;
}

.el-table .el-table__cell {
  border-bottom: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .el-table {
    font-size: 12px;
  }

  .fee-summary-section {
    padding: 10px;
  }

  .fee-summary-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .fee-label {
    margin-bottom: 5px;
  }
}
</style>
