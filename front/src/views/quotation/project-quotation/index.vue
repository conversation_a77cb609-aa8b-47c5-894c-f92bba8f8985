<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编号" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务类别" prop="businessType">
        <el-select
          v-model="queryParams.businessType"
          placeholder="请选择业务类别"
          clearable
          style="width: 200px"
        >
          <el-option label="一般采样" value="sampling" />
          <el-option label="送样" value="sample" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="项目状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['quotation:project-quotation:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['quotation:project-quotation:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['quotation:project-quotation:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['quotation:project-quotation:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectQuotationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目编号" align="center" prop="projectCode" width="120" />
      <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
      <el-table-column label="业务类别" align="center" prop="businessType" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.businessType === 'sampling' ? 'primary' : 'success'">
            {{ scope.row.businessType === 'sampling' ? '一般采样' : '送样' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="合同编号" align="center" prop="contractCode" width="120" />
      <el-table-column label="客户名称" align="center" prop="customerName" :show-overflow-tooltip="true" />
      <el-table-column label="项目负责人" align="center" prop="projectManager" width="100" />

      <el-table-column label="项目状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="statusOptions" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleViewQuotation(scope.row)"
            v-hasPermi="['quotation:project-quotation:query']"
          >查看报价</el-button>
          <el-button
            v-if="canSubmitApproval(scope.row)"
            type="text"
            icon="Check"
            @click="handleSubmitApproval(scope.row)"
            v-hasPermi="['quotation:project-quotation:edit']"
            style="color: #67C23A"
          >提交审批</el-button>
          <el-button
            v-if="canEdit(scope.row)"
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['quotation:project-quotation:edit']"
          >修改项目</el-button>
          <el-button
            v-if="canDelete(scope.row)"
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['quotation:project-quotation:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看报价弹框 -->
    <edit-quotation-dialog
      :visible="editQuotationOpen"
      @update:visible="editQuotationOpen = $event"
      :quotation-id="currentQuotationId"
      @refresh="getList"
    />

    <!-- 查看项目报价弹框 -->
    <view-quotation-dialog
      :visible="viewQuotationOpen"
      @update:visible="viewQuotationOpen = $event"
      :quotation-id="currentQuotationId"
      @edit="handleEditFromView"
      @refresh="getList"
    />

    <!-- 修改项目信息弹框 -->
    <edit-project-info-dialog
      :visible="editProjectInfoOpen"
      @update:visible="editProjectInfoOpen = $event"
      :quotation-id="currentQuotationId"
      @refresh="getList"
    />

    <!-- 新增项目报价弹框 -->
    <add-project-quotation-dialog
      :visible="addQuotationOpen"
      @update:visible="addQuotationOpen = $event"
      @refresh="getList"
    />

    <!-- 业务类型选择对话框 -->
    <el-dialog
      v-model="businessTypeDialogVisible"
      title="选择业务类型"
      width="500px"
      :close-on-click-modal="false"
    >
      <div style="padding: 20px 0;">
        <p style="margin-bottom: 20px; color: #606266;">
          项目：<strong>{{ currentSubmitProjectName }}</strong>
        </p>
        <p style="margin-bottom: 20px; color: #909399;">
          请选择该项目的业务类型，不同业务类型的审批流程有所不同：
        </p>
        <el-form label-width="100px">
          <el-form-item label="业务类型">
            <el-radio-group v-model="selectedBusinessType">
              <el-radio label="sampling" size="large">
                <div style="margin-left: 8px;">
                  <div style="font-weight: bold; color: #409EFF;">一般采样</div>
                  <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                    需要市场审批 → 实验室审批 → 现场审批（三级审批）
                  </div>
                </div>
              </el-radio>
              <el-radio label="sample" size="large" style="margin-top: 15px;">
                <div style="margin-left: 8px;">
                  <div style="font-weight: bold; color: #67C23A;">送样</div>
                  <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                    需要市场审批 → 实验室审批（二级审批，现场审批可选）
                  </div>
                </div>
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelSubmitApproval">取消</el-button>
          <el-button type="primary" @click="confirmSubmitApproval" :loading="loading">
            确认提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  pageProjectQuotation,
  delProjectQuotation,
  exportProjectQuotation,
  submitProjectQuotationApproval
} from "@/api/quotation/projectQuotation";
import EditQuotationDialog from './components/EditQuotationDialog.vue';
import ViewQuotationDialog from './components/ViewQuotationDialog.vue';
import EditProjectInfoDialog from './components/EditProjectInfoDialog.vue';
import AddProjectQuotationDialog from './components/AddProjectQuotationDialog.vue';
import { useRouter } from 'vue-router'
import useUserStore from '@/store/modules/user'

const router = useRouter()
const { proxy } = getCurrentInstance()
const userStore = useUserStore()

// 遮罩层
const loading = ref(false)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 项目报价表格数据
const projectQuotationList = ref([])
// 日期范围
const dateRange = ref([])
// 状态数据字典
const statusOptions = ref([
  { value: '0', label: '草稿' },
  { value: '1', label: '待审核' },
  { value: '2', label: '已审核' },
  { value: '3', label: '已撤回' },
  { value: '4', label: '已拒绝' }
])

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectName: undefined,
  projectCode: undefined,
  customerName: undefined,
  businessType: undefined,
  status: undefined,
  beginTime: undefined,
  endTime: undefined
})

/** 查询项目报价列表 */
function getList() {
  loading.value = true
  if (dateRange.value && dateRange.value.length > 0) {
    queryParams.value.beginTime = dateRange.value[0]
    queryParams.value.endTime = dateRange.value[1]
  } else {
    queryParams.value.beginTime = undefined
    queryParams.value.endTime = undefined
  }
  pageProjectQuotation(queryParams.value).then(response => {
    projectQuotationList.value = response.data.rows
    total.value = response.data.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  // 打开新增项目报价弹框
  addQuotationOpen.value = true
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const id = row.id || ids.value[0]
  // 弹出修改项目信息弹框
  currentQuotationId.value = id
  editProjectInfoOpen.value = true
  // 不再跳转到新页面
  // router.push({ path: `/quotation/project-quotation/edit/${id}` })
}


/** 删除按钮操作 */
function handleDelete(row) {
  const projectIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除项目报价编号为"' + projectIds + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delProjectQuotation(projectIds)
  }).then(() => {
    getList()
    ElMessage.success("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  const queryParams = {
    projectName: queryParams.value.projectName,
    projectCode: queryParams.value.projectCode,
    customerName: queryParams.value.customerName,
    status: queryParams.value.status,
    beginTime: queryParams.value.beginTime,
    endTime: queryParams.value.endTime
  }
  ElMessageBox.confirm('是否确认导出所有项目报价数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return exportProjectQuotation(queryParams)
  }).then(response => {
    proxy.download(response)
  }).catch(() => {})
}



// 查看报价对话框
const editQuotationOpen = ref(false)
// 查看项目报价对话框
const viewQuotationOpen = ref(false)
// 修改项目信息对话框
const editProjectInfoOpen = ref(false)
// 新增项目报价对话框
const addQuotationOpen = ref(false)
// 当前查看的报价ID
const currentQuotationId = ref(null)

/** 查看报价按钮操作 */
function handleViewQuotation(row) {
  const id = row.id || ids.value[0]
  currentQuotationId.value = id
  viewQuotationOpen.value = true
}

/** 编辑报价按钮操作 */
function handleEditQuotation(row) {
  const id = row.id || ids.value[0]
  currentQuotationId.value = id
  editQuotationOpen.value = true
}

/** 从查看页面跳转到编辑页面 */
function handleEditFromView(id) {
  currentQuotationId.value = id
  editQuotationOpen.value = true
}

/** 检查是否可以编辑 */
function canEdit(row) {
  // 管理员可以编辑所有项目
  if (userStore.roles.includes('admin')) {
    return true
  }
  // 只有创建人可以编辑
  return row.createBy === userStore.name
}

/** 检查是否可以删除 */
function canDelete(row) {
  // 管理员可以删除所有项目
  if (userStore.roles.includes('admin')) {
    return true
  }
  // 只有创建人可以删除
  return row.createBy === userStore.name
}

/** 检查是否可以提交审批 */
function canSubmitApproval(row) {
  // 只有草稿状态的项目才能提交审批
  if (row.status !== '0') {
    return false
  }
  // 管理员可以提交所有项目
  if (userStore.roles.includes('admin')) {
    return true
  }
  // 只有创建人可以提交审批
  return row.createBy === userStore.name
}

/** 提交审批按钮操作 */
function handleSubmitApproval(row) {
  const id = row.id
  const projectName = row.projectName

  ElMessageBox.confirm(
    `确认提交项目"${projectName}"的审批申请吗？提交后将进入市场审批阶段。`,
    "提交审批确认",
    {
      confirmButtonText: "确定提交",
      cancelButtonText: "取消",
      type: "warning",
      customClass: "submit-approval-dialog"
    }
  ).then(() => {
    // 显示业务类型选择对话框
    showBusinessTypeDialog(id, projectName)
  }).catch(() => {
    // 用户取消操作
  })
}

// 业务类型选择对话框相关
const businessTypeDialogVisible = ref(false)
const selectedBusinessType = ref('sampling')
const currentSubmitId = ref(null)
const currentSubmitProjectName = ref('')

/** 显示业务类型选择对话框 */
function showBusinessTypeDialog(id, projectName) {
  currentSubmitId.value = id
  currentSubmitProjectName.value = projectName
  selectedBusinessType.value = 'sampling' // 默认选择一般采样
  businessTypeDialogVisible.value = true
}

/** 确认提交审批 */
function confirmSubmitApproval() {
  const id = currentSubmitId.value
  const businessType = selectedBusinessType.value

  loading.value = true
  submitProjectQuotationApproval(id, businessType).then(response => {
    ElMessage.success("项目已成功提交审批，进入市场审批阶段")
    businessTypeDialogVisible.value = false
    getList() // 刷新列表
  }).catch(error => {
    ElMessage.error(error.message || "提交审批失败")
  }).finally(() => {
    loading.value = false
  })
}

/** 取消提交审批 */
function cancelSubmitApproval() {
  businessTypeDialogVisible.value = false
  currentSubmitId.value = null
  currentSubmitProjectName.value = ''
  selectedBusinessType.value = 'sampling'
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.box-card {
  margin-bottom: 20px;
}
</style>