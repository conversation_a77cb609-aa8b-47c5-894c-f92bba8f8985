#!/usr/bin/env python3
"""
测试详细状态功能
"""

import sys
import asyncio
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, '.')

async def test_detailed_status():
    """
    测试详细状态功能
    """
    print("测试项目报价详细状态功能...")

    try:
        # 导入必要模块
        from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
        from sqlalchemy.orm import sessionmaker
        from config.env import DataBaseConfig
        from module_admin.entity.do.user_do import SysUser
        from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
        from module_quotation.service.project_quotation_service import ProjectQuotationService
        from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService

        print("✅ 模块导入成功")

        # 构建数据库URL
        database_url = f"mysql+asyncmy://{DataBaseConfig.db_username}:{DataBaseConfig.db_password}@{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"

        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=False)
        TestSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

        async with TestSessionLocal() as session:
            # 创建测试用户
            test_user = SysUser(
                user_name="test_status_user",
                nick_name="测试状态用户",
                password="test_password",
                status="0",
                del_flag="0",
                create_by="admin",
                create_time=datetime.now()
            )
            session.add(test_user)
            await session.flush()

            # 创建测试项目报价
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            test_quotation = ProjectQuotation(
                project_name="测试详细状态项目",
                project_code=f"STATUS{timestamp}",
                business_type="sampling",  # 一般采样
                status="1",  # 待审核状态
                customer_name="测试客户",
                create_by=test_user.user_id,
                create_time=datetime.now()
            )
            session.add(test_quotation)
            await session.flush()
            await session.commit()

            print(f"✅ 创建测试数据成功，项目ID: {test_quotation.id}")

            # 创建当前用户模型
            current_user = CurrentUserModel(
                permissions=[],
                roles=[],
                user=UserInfoModel(
                    user_id=test_user.user_id,
                    user_name=test_user.user_name,
                    nick_name=test_user.nick_name,
                    status=test_user.status,
                    del_flag=test_user.del_flag
                )
            )

            # 测试服务
            quotation_service = ProjectQuotationService(session)
            approval_service = ProjectQuotationApprovalService(session)

            # 测试场景1：市场审批阶段
            print("\n🔄 测试场景1：市场审批阶段")

            # 创建市场审批记录（待审批）
            market_record = ProjectQuotationApprovalRecord(
                project_quotation_id=test_quotation.id,
                approver_type="market",
                approver_user_id=test_user.user_id,
                approval_stage=1,
                approval_status="pending",
                is_required="1",
                create_by=current_user.user.user_id,
                create_time=datetime.now()
            )
            session.add(market_record)

            # 创建实验室审批记录（未开始）
            lab_record = ProjectQuotationApprovalRecord(
                project_quotation_id=test_quotation.id,
                approver_type="lab",
                approver_user_id=test_user.user_id,
                approval_stage=2,
                approval_status="pending",
                is_required="1",
                create_by=current_user.user.user_id,
                create_time=datetime.now()
            )
            session.add(lab_record)

            # 创建现场审批记录（未开始）
            field_record = ProjectQuotationApprovalRecord(
                project_quotation_id=test_quotation.id,
                approver_type="field",
                approver_user_id=test_user.user_id,
                approval_stage=2,
                approval_status="pending",
                is_required="1",
                create_by=current_user.user.user_id,
                create_time=datetime.now()
            )
            session.add(field_record)
            await session.commit()

            # 获取详细状态
            detailed_status = await quotation_service.get_project_quotation_detailed_status(test_quotation.id)
            detailed_label = quotation_service.get_detailed_status_label(detailed_status)
            print(f"   详细状态: {detailed_status}")
            print(f"   状态标签: {detailed_label}")

            # 测试场景2：实验室和现场审批阶段
            print("\n🔄 测试场景2：实验室和现场审批阶段")

            # 市场审批通过
            market_record.approval_status = "approved"
            market_record.approval_time = datetime.now()
            await session.commit()

            # 获取详细状态
            detailed_status = await quotation_service.get_project_quotation_detailed_status(test_quotation.id)
            detailed_label = quotation_service.get_detailed_status_label(detailed_status)
            print(f"   详细状态: {detailed_status}")
            print(f"   状态标签: {detailed_label}")

            # 测试场景3：只需要实验室审批
            print("\n🔄 测试场景3：只需要实验室审批")

            # 现场审批通过
            field_record.approval_status = "approved"
            field_record.approval_time = datetime.now()
            await session.commit()

            # 获取详细状态
            detailed_status = await quotation_service.get_project_quotation_detailed_status(test_quotation.id)
            detailed_label = quotation_service.get_detailed_status_label(detailed_status)
            print(f"   详细状态: {detailed_status}")
            print(f"   状态标签: {detailed_label}")

            # 测试场景4：所有审批完成
            print("\n🔄 测试场景4：所有审批完成")

            # 实验室审批通过
            lab_record.approval_status = "approved"
            lab_record.approval_time = datetime.now()
            await session.commit()

            # 更新项目状态为已审核
            test_quotation.status = "2"
            await session.commit()

            # 获取详细状态
            detailed_status = await quotation_service.get_project_quotation_detailed_status(test_quotation.id)
            detailed_label = quotation_service.get_detailed_status_label(detailed_status)
            print(f"   详细状态: {detailed_status}")
            print(f"   状态标签: {detailed_label}")

            # 测试送样项目
            print("\n🔄 测试场景5：送样项目")

            # 创建送样项目
            sample_quotation = ProjectQuotation(
                project_name="测试送样项目",
                project_code=f"SAMPLE{timestamp}",
                business_type="sample",  # 送样
                status="1",  # 待审核状态
                customer_name="测试客户",
                create_by=test_user.user_id,
                create_time=datetime.now()
            )
            session.add(sample_quotation)
            await session.flush()

            # 创建送样项目的审批记录
            sample_market_record = ProjectQuotationApprovalRecord(
                project_quotation_id=sample_quotation.id,
                approver_type="market",
                approver_user_id=test_user.user_id,
                approval_stage=1,
                approval_status="approved",  # 市场审批已通过
                is_required="1",
                create_by=current_user.user.user_id,
                create_time=datetime.now()
            )
            session.add(sample_market_record)

            sample_lab_record = ProjectQuotationApprovalRecord(
                project_quotation_id=sample_quotation.id,
                approver_type="lab",
                approver_user_id=test_user.user_id,
                approval_stage=2,
                approval_status="pending",  # 实验室审批待处理
                is_required="1",
                create_by=current_user.user.user_id,
                create_time=datetime.now()
            )
            session.add(sample_lab_record)

            sample_field_record = ProjectQuotationApprovalRecord(
                project_quotation_id=sample_quotation.id,
                approver_type="field",
                approver_user_id=test_user.user_id,
                approval_stage=2,
                approval_status="pending",
                is_required="0",  # 现场审批可选
                create_by=current_user.user.user_id,
                create_time=datetime.now()
            )
            session.add(sample_field_record)
            await session.commit()

            # 获取送样项目的详细状态
            detailed_status = await quotation_service.get_project_quotation_detailed_status(sample_quotation.id)
            detailed_label = quotation_service.get_detailed_status_label(detailed_status)
            print(f"   送样项目详细状态: {detailed_status}")
            print(f"   送样项目状态标签: {detailed_label}")

            # 清理测试数据
            await session.delete(test_quotation)
            await session.delete(sample_quotation)
            await session.delete(test_user)
            await session.commit()
            print("\n✅ 清理测试数据成功")

        await engine.dispose()
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """
    主函数
    """
    print("开始测试详细状态功能\n")

    if await test_detailed_status():
        print("\n🎉 详细状态功能测试通过！")
        print("\n✅ 功能验证:")
        print("  • 市场审批阶段显示：待审核（市场）")
        print("  • 实验室和现场审批阶段显示：待审核（实验室|现场）")
        print("  • 只需实验室审批显示：待审核（实验室）")
        print("  • 只需现场审批显示：待审核（现场）")
        print("  • 所有审批完成显示：已审核")
        print("  • 送样项目正确处理现场审批可选逻辑")

        print("\n📋 状态说明:")
        print("  • 草稿：项目创建后的初始状态")
        print("  • 待审核（市场）：等待市场部门审批")
        print("  • 待审核（实验室）：等待实验室审批")
        print("  • 待审核（现场）：等待现场审批")
        print("  • 待审核（实验室|现场）：等待实验室和现场审批")
        print("  • 已审核：所有必需审批都已通过")
        print("  • 已撤回：项目已被撤回")
        print("  • 已拒绝：审批被拒绝")

        print("\n🚀 功能已就绪，前端将显示详细的审批阶段信息！")
        return True
    else:
        print("\n❌ 详细状态功能测试失败")
        return False


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
