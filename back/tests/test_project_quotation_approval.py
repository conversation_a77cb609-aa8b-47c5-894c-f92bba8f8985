"""
项目报价审批功能单元测试
"""

import pytest
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.do.user_do import SysUser, SysUserRole
from module_admin.entity.do.role_do import SysRole
from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
from module_quotation.entity.vo.project_quotation_approval_record_vo import ApprovalActionModel
from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService


class TestProjectQuotationApproval:
    """
    项目报价审批测试类
    """

    @pytest.fixture
    async def setup_test_data(self, db_session: AsyncSession):
        """
        设置测试数据
        """
        # 创建测试角色
        market_role = SysRole(
            role_name="市场审批人员",
            role_key="market-approver",
            role_sort=10,
            status="0"
        )
        lab_role = SysRole(
            role_name="实验室审批人员", 
            role_key="lab-approver",
            role_sort=11,
            status="0"
        )
        field_role = SysRole(
            role_name="现场审批人员",
            role_key="field-approver", 
            role_sort=12,
            status="0"
        )
        
        db_session.add_all([market_role, lab_role, field_role])
        await db_session.flush()

        # 创建测试用户
        market_user = SysUser(
            user_name="market_user",
            nick_name="市场用户",
            status="0",
            del_flag="0"
        )
        lab_user = SysUser(
            user_name="lab_user",
            nick_name="实验室用户",
            status="0",
            del_flag="0"
        )
        field_user = SysUser(
            user_name="field_user",
            nick_name="现场用户",
            status="0",
            del_flag="0"
        )
        
        db_session.add_all([market_user, lab_user, field_user])
        await db_session.flush()

        # 创建用户角色关联
        user_roles = [
            SysUserRole(user_id=market_user.user_id, role_id=market_role.role_id),
            SysUserRole(user_id=lab_user.user_id, role_id=lab_role.role_id),
            SysUserRole(user_id=field_user.user_id, role_id=field_role.role_id)
        ]
        db_session.add_all(user_roles)

        # 创建测试项目报价
        quotation = ProjectQuotation(
            project_name="测试项目",
            project_code="TEST001",
            business_type="sampling",
            status="0",
            create_by=market_user.user_id,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()

        await db_session.commit()

        return {
            "quotation": quotation,
            "market_user": market_user,
            "lab_user": lab_user,
            "field_user": field_user,
            "market_role": market_role,
            "lab_role": lab_role,
            "field_role": field_role
        }

    async def test_init_approval_records_sampling(self, db_session: AsyncSession, setup_test_data):
        """
        测试一般采样项目的审批记录初始化
        """
        test_data = await setup_test_data
        quotation = test_data["quotation"]
        market_user = test_data["market_user"]

        # 创建当前用户模型
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )

        # 初始化审批记录
        approval_service = ProjectQuotationApprovalService(db_session)
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)

        # 验证审批记录是否正确创建
        records = await approval_service.dao.get_approval_records_by_quotation_id(quotation.id)
        
        # 应该有3类审批记录：市场、实验室、现场
        assert len(records) == 3
        
        # 验证市场审批记录
        market_records = [r for r in records if r.approver_type == "market"]
        assert len(market_records) == 1
        assert market_records[0].approval_stage == 1
        assert market_records[0].is_required == "1"

        # 验证实验室审批记录
        lab_records = [r for r in records if r.approver_type == "lab"]
        assert len(lab_records) == 1
        assert lab_records[0].approval_stage == 2
        assert lab_records[0].is_required == "1"

        # 验证现场审批记录
        field_records = [r for r in records if r.approver_type == "field"]
        assert len(field_records) == 1
        assert field_records[0].approval_stage == 2
        assert field_records[0].is_required == "1"  # 一般采样需要现场审批

    async def test_init_approval_records_sample(self, db_session: AsyncSession, setup_test_data):
        """
        测试送样项目的审批记录初始化
        """
        test_data = await setup_test_data
        quotation = test_data["quotation"]
        quotation.business_type = "sample"  # 修改为送样
        market_user = test_data["market_user"]

        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )

        approval_service = ProjectQuotationApprovalService(db_session)
        await approval_service.init_approval_records(quotation.id, "sample", current_user)

        records = await approval_service.dao.get_approval_records_by_quotation_id(quotation.id)
        
        # 验证现场审批记录不是必需的
        field_records = [r for r in records if r.approver_type == "field"]
        assert len(field_records) == 1
        assert field_records[0].is_required == "0"  # 送样不需要现场审批

    async def test_approval_workflow(self, db_session: AsyncSession, setup_test_data):
        """
        测试完整的审批流程
        """
        test_data = await setup_test_data
        quotation = test_data["quotation"]
        market_user = test_data["market_user"]
        lab_user = test_data["lab_user"]
        field_user = test_data["field_user"]

        approval_service = ProjectQuotationApprovalService(db_session)

        # 初始化审批记录
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)

        # 提交审批
        await approval_service.submit_for_approval(quotation.id, current_user)

        # 市场审批通过
        market_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        market_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="市场审批通过"
        )
        await approval_service.perform_approval(quotation.id, market_action, market_current_user)

        # 实验室审批通过
        lab_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=lab_user.user_id, user_name=lab_user.user_name)
        )
        lab_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="实验室审批通过"
        )
        await approval_service.perform_approval(quotation.id, lab_action, lab_current_user)

        # 现场审批通过
        field_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=field_user.user_id, user_name=field_user.user_name)
        )
        field_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="现场审批通过"
        )
        await approval_service.perform_approval(quotation.id, field_action, field_current_user)

        # 验证最终状态
        approval_status = await approval_service.get_approval_status(quotation.id)
        assert approval_status.overall_status == "2"  # 已审核

    async def test_approval_rejection(self, db_session: AsyncSession, setup_test_data):
        """
        测试审批拒绝流程
        """
        test_data = await setup_test_data
        quotation = test_data["quotation"]
        market_user = test_data["market_user"]

        approval_service = ProjectQuotationApprovalService(db_session)

        # 初始化审批记录
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)

        # 提交审批
        await approval_service.submit_for_approval(quotation.id, current_user)

        # 市场审批拒绝
        market_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="rejected",
            approval_opinion="市场审批拒绝"
        )
        await approval_service.perform_approval(quotation.id, market_action, current_user)

        # 验证最终状态
        approval_status = await approval_service.get_approval_status(quotation.id)
        assert approval_status.overall_status == "4"  # 已拒绝
