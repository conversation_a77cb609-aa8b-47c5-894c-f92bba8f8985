"""
项目报价审批DAO层测试
"""

import pytest
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.do.user_do import SysUser, SysUserRole
from module_admin.entity.do.role_do import SysRole
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
from module_quotation.dao.project_quotation_approval_record_dao import ProjectQuotationApprovalRecordDao
from module_quotation.entity.vo.project_quotation_approval_record_vo import (
    ProjectQuotationApprovalRecordPageQueryModel,
    ProjectQuotationApprovalRecordQueryModel
)


class TestApprovalDAO:
    """
    审批DAO测试类
    """

    @pytest.fixture
    async def setup_dao_test_data(self, db: AsyncSession):
        """
        设置DAO测试数据
        """
        # 创建测试用户
        user1 = SysUser(
            user_id=1,
            user_name="user1",
            nick_name="用户1",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        user2 = SysUser(
            user_id=2,
            user_name="user2",
            nick_name="用户2",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        
        db.add_all([user1, user2])
        await db.flush()

        # 创建测试项目报价
        quotation1 = ProjectQuotation(
            id=1,
            project_name="测试项目1",
            project_code="TEST001",
            business_type="sampling",
            status="0",
            customer_name="测试客户",
            create_by=user1.user_id,
            create_time=datetime.now()
        )
        
        quotation2 = ProjectQuotation(
            id=2,
            project_name="测试项目2",
            project_code="TEST002",
            business_type="sample",
            status="0",
            customer_name="测试客户",
            create_by=user1.user_id,
            create_time=datetime.now()
        )
        
        db.add_all([quotation1, quotation2])
        await db.flush()

        # 创建测试审批记录
        approval_records = [
            ProjectQuotationApprovalRecord(
                project_quotation_id=1,
                approver_type="market",
                approver_user_id=1,
                approval_stage=1,
                is_required="1",
                approval_status="pending",
                create_by=user1.user_id,
                create_time=datetime.now()
            ),
            ProjectQuotationApprovalRecord(
                project_quotation_id=1,
                approver_type="lab",
                approver_user_id=2,
                approval_stage=2,
                is_required="1",
                approval_status="pending",
                create_by=user1.user_id,
                create_time=datetime.now()
            ),
            ProjectQuotationApprovalRecord(
                project_quotation_id=1,
                approver_type="field",
                approver_user_id=1,
                approval_stage=2,
                is_required="1",
                approval_status="pending",
                create_by=user1.user_id,
                create_time=datetime.now()
            ),
            ProjectQuotationApprovalRecord(
                project_quotation_id=2,
                approver_type="market",
                approver_user_id=1,
                approval_stage=1,
                is_required="1",
                approval_status="approved",
                approval_time=datetime.now(),
                create_by=user1.user_id,
                create_time=datetime.now()
            )
        ]
        
        db.add_all(approval_records)
        await db.flush()
        await db.commit()

        return {
            "quotation1": quotation1,
            "quotation2": quotation2,
            "user1": user1,
            "user2": user2,
            "approval_records": approval_records
        }

    @pytest.mark.asyncio
    async def test_get_approval_records_by_quotation_id(self, setup_dao_test_data):
        """
        测试根据项目报价ID获取审批记录
        """
        test_data = await setup_dao_test_data
        quotation1 = test_data["quotation1"]
        db = quotation1.__dict__['_sa_instance_state'].session

        dao = ProjectQuotationApprovalRecordDao(db)
        records = await dao.get_approval_records_by_quotation_id(quotation1.id)

        # 项目1应该有3条审批记录
        assert len(records) == 3
        
        # 验证记录按阶段和类型排序
        assert records[0].approval_stage == 1  # 市场审批在第一阶段
        assert records[1].approval_stage == 2  # 实验室和现场审批在第二阶段
        assert records[2].approval_stage == 2

    @pytest.mark.asyncio
    async def test_get_approval_record_by_user_and_quotation(self, setup_dao_test_data):
        """
        测试根据用户和项目获取审批记录
        """
        test_data = await setup_dao_test_data
        quotation1 = test_data["quotation1"]
        user1 = test_data["user1"]
        db = quotation1.__dict__['_sa_instance_state'].session

        dao = ProjectQuotationApprovalRecordDao(db)
        record = await dao.get_approval_record_by_user_and_quotation(quotation1.id, user1.user_id)

        # 用户1在项目1中应该有审批记录
        assert record is not None
        assert record.approver_user_id == user1.user_id
        assert record.project_quotation_id == quotation1.id

    @pytest.mark.asyncio
    async def test_get_pending_approvals_by_user(self, setup_dao_test_data):
        """
        测试获取用户的待审批记录
        """
        test_data = await setup_dao_test_data
        user1 = test_data["user1"]
        db = test_data["quotation1"].__dict__['_sa_instance_state'].session

        dao = ProjectQuotationApprovalRecordDao(db)
        pending_records = await dao.get_pending_approvals_by_user(user1.user_id)

        # 用户1应该有待审批的记录
        assert len(pending_records) > 0
        for record in pending_records:
            assert record.approval_status == "pending"
            assert record.approver_user_id == user1.user_id
            assert record.is_required == "1"

    @pytest.mark.asyncio
    async def test_get_approval_records_by_stage(self, setup_dao_test_data):
        """
        测试根据审批阶段获取记录
        """
        test_data = await setup_dao_test_data
        quotation1 = test_data["quotation1"]
        db = quotation1.__dict__['_sa_instance_state'].session

        dao = ProjectQuotationApprovalRecordDao(db)
        
        # 获取第一阶段的审批记录
        stage1_records = await dao.get_approval_records_by_stage(quotation1.id, 1)
        assert len(stage1_records) == 1
        assert stage1_records[0].approver_type == "market"

        # 获取第二阶段的审批记录
        stage2_records = await dao.get_approval_records_by_stage(quotation1.id, 2)
        assert len(stage2_records) == 2
        stage2_types = [r.approver_type for r in stage2_records]
        assert "lab" in stage2_types
        assert "field" in stage2_types

    @pytest.mark.asyncio
    async def test_get_required_approval_records_by_stage(self, setup_dao_test_data):
        """
        测试获取必需的审批记录
        """
        test_data = await setup_dao_test_data
        quotation1 = test_data["quotation1"]
        db = quotation1.__dict__['_sa_instance_state'].session

        dao = ProjectQuotationApprovalRecordDao(db)
        required_records = await dao.get_required_approval_records_by_stage(quotation1.id, 2)

        # 第二阶段的必需审批记录
        assert len(required_records) == 2
        for record in required_records:
            assert record.is_required == "1"
            assert record.approval_stage == 2

    @pytest.mark.asyncio
    async def test_delete_approval_records_by_quotation_id(self, setup_dao_test_data):
        """
        测试删除项目的所有审批记录
        """
        test_data = await setup_dao_test_data
        quotation1 = test_data["quotation1"]
        db = quotation1.__dict__['_sa_instance_state'].session

        dao = ProjectQuotationApprovalRecordDao(db)
        
        # 删除前确认有记录
        records_before = await dao.get_approval_records_by_quotation_id(quotation1.id)
        assert len(records_before) > 0

        # 删除记录
        await dao.delete_approval_records_by_quotation_id(quotation1.id)
        await db.commit()

        # 删除后确认记录已清空
        records_after = await dao.get_approval_records_by_quotation_id(quotation1.id)
        assert len(records_after) == 0

    @pytest.mark.asyncio
    async def test_get_approval_records_page(self, setup_dao_test_data):
        """
        测试分页查询审批记录
        """
        test_data = await setup_dao_test_data
        db = test_data["quotation1"].__dict__['_sa_instance_state'].session

        dao = ProjectQuotationApprovalRecordDao(db)
        
        # 创建查询参数
        query_params = ProjectQuotationApprovalRecordPageQueryModel(
            page_num=1,
            page_size=10,
            approval_status="pending"
        )

        result = await dao.get_approval_records_page(query_params)

        assert "total" in result
        assert "rows" in result
        assert result["total"] >= 0
        assert isinstance(result["rows"], list)

        # 验证查询条件生效
        for record in result["rows"]:
            assert record.approval_status == "pending"

    @pytest.mark.asyncio
    async def test_get_approval_records_list(self, setup_dao_test_data):
        """
        测试查询审批记录列表
        """
        test_data = await setup_dao_test_data
        quotation1 = test_data["quotation1"]
        db = quotation1.__dict__['_sa_instance_state'].session

        dao = ProjectQuotationApprovalRecordDao(db)
        
        # 创建查询参数
        query_params = ProjectQuotationApprovalRecordQueryModel(
            project_quotation_id=quotation1.id,
            approver_type="market"
        )

        records = await dao.get_approval_records_list(query_params)

        assert len(records) == 1
        assert records[0].project_quotation_id == quotation1.id
        assert records[0].approver_type == "market"

    @pytest.mark.asyncio
    async def test_dao_crud_operations(self, setup_dao_test_data):
        """
        测试DAO的基本CRUD操作
        """
        test_data = await setup_dao_test_data
        user1 = test_data["user1"]
        db = test_data["quotation1"].__dict__['_sa_instance_state'].session

        dao = ProjectQuotationApprovalRecordDao(db)

        # 创建新的审批记录
        new_record = ProjectQuotationApprovalRecord(
            project_quotation_id=999,
            approver_type="test",
            approver_user_id=user1.user_id,
            approval_stage=1,
            is_required="1",
            approval_status="pending",
            create_by=user1.user_id,
            create_time=datetime.now()
        )

        # 添加记录
        created_record = await dao.add(new_record)
        await db.commit()
        assert created_record.id is not None

        # 查询记录
        found_record = await dao.get_by_id(created_record.id)
        assert found_record is not None
        assert found_record.approver_type == "test"

        # 更新记录
        found_record.approval_status = "approved"
        found_record.approval_time = datetime.now()
        updated_record = await dao.update(found_record)
        await db.commit()
        assert updated_record.approval_status == "approved"

        # 删除记录
        await dao.delete_by_id(created_record.id)
        await db.commit()
        deleted_record = await dao.get_by_id(created_record.id)
        assert deleted_record is None
