-- 添加业务类型字段到项目报价表
-- 用于支持审批流程中的业务类型区分

-- 添加 business_type 字段
ALTER TABLE project_quotation 
ADD COLUMN business_type VARCHAR(50) DEFAULT 'sampling' COMMENT '业务类型：sampling-一般采样，sample-送样';

-- 更新现有记录的默认值
UPDATE project_quotation 
SET business_type = 'sampling' 
WHERE business_type IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_project_quotation_business_type ON project_quotation(business_type);

-- 验证字段添加成功
SELECT COUNT(*) as total_records, 
       COUNT(CASE WHEN business_type IS NOT NULL THEN 1 END) as records_with_business_type
FROM project_quotation;
