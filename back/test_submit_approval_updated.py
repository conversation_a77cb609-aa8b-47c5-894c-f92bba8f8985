#!/usr/bin/env python3
"""
测试更新后的提交审批功能
"""

import sys
import asyncio
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, '.')

async def test_submit_approval_without_business_type():
    """
    测试不需要选择业务类型的提交审批功能
    """
    print("测试更新后的提交审批功能...")

    try:
        # 导入必要模块
        from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
        from sqlalchemy.orm import sessionmaker
        from config.env import DataBaseConfig
        from module_admin.entity.do.user_do import SysUser
        from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_quotation.service.project_quotation_service import ProjectQuotationService
        from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService

        print("✅ 模块导入成功")

        # 构建数据库URL
        database_url = f"mysql+asyncmy://{DataBaseConfig.db_username}:{DataBaseConfig.db_password}@{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"

        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=False)
        TestSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

        async with TestSessionLocal() as session:
            # 创建测试用户
            test_user = SysUser(
                user_name="test_submit_user_v2",
                nick_name="测试提交用户V2",
                password="test_password",
                status="0",
                del_flag="0",
                create_by="admin",
                create_time=datetime.now()
            )
            session.add(test_user)
            await session.flush()

            # 测试一般采样项目
            print("\n📋 测试一般采样项目...")
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            sampling_quotation = ProjectQuotation(
                project_name="测试一般采样项目",
                project_code=f"SAMPLING{timestamp}",
                business_type="sampling",  # 一般采样
                status="0",  # 草稿状态
                customer_name="测试客户",
                create_by=test_user.user_id,
                create_time=datetime.now()
            )
            session.add(sampling_quotation)
            await session.flush()

            # 测试送样项目
            print("📋 测试送样项目...")
            sample_quotation = ProjectQuotation(
                project_name="测试送样项目",
                project_code=f"SAMPLE{timestamp}",
                business_type="sample",  # 送样
                status="0",  # 草稿状态
                customer_name="测试客户",
                create_by=test_user.user_id,
                create_time=datetime.now()
            )
            session.add(sample_quotation)
            await session.flush()
            await session.commit()

            print(f"✅ 创建测试数据成功")
            print(f"   一般采样项目ID: {sampling_quotation.id}")
            print(f"   送样项目ID: {sample_quotation.id}")

            # 创建当前用户模型
            current_user = CurrentUserModel(
                permissions=[],
                roles=[],
                user=UserInfoModel(
                    user_id=test_user.user_id,
                    user_name=test_user.user_name,
                    nick_name=test_user.nick_name,
                    status=test_user.status,
                    del_flag=test_user.del_flag
                )
            )

            # 测试项目报价服务
            quotation_service = ProjectQuotationService(session)
            approval_service = ProjectQuotationApprovalService(session)

            # 测试一般采样项目的提交审批
            print("\n🔄 测试一般采样项目提交审批...")

            # 获取项目报价（模拟API调用）
            sampling_data = await quotation_service.get_project_quotation(sampling_quotation.id)
            business_type = sampling_data.get("businessType", "sampling")
            print(f"   从项目中获取业务类型: {business_type}")

            # 初始化审批记录
            await approval_service.init_approval_records(sampling_quotation.id, business_type, current_user)
            print("   ✅ 初始化审批记录成功")

            # 提交审批
            await approval_service.submit_for_approval(sampling_quotation.id, current_user)
            print("   ✅ 提交审批成功")

            # 更新状态
            await quotation_service.update_project_quotation_status(sampling_quotation.id, "1", current_user)
            print("   ✅ 更新状态成功")

            # 验证审批记录
            approval_status = await approval_service.get_approval_status(sampling_quotation.id)
            print(f"   ✅ 审批状态: {approval_status.overall_status}")
            print(f"   ✅ 审批记录数: {len(approval_status.approval_records)}")

            # 检查现场审批是否为必需
            field_required = any(
                record.approver_type == "field" and record.approval_status == "pending"
                for record in approval_status.approval_records
            )
            print(f"   ✅ 现场审批是否必需: {'是' if field_required else '否'}")

            # 测试送样项目的提交审批
            print("\n🔄 测试送样项目提交审批...")

            # 获取项目报价（模拟API调用）
            sample_data = await quotation_service.get_project_quotation(sample_quotation.id)
            business_type = sample_data.get("businessType", "sampling")
            print(f"   从项目中获取业务类型: {business_type}")

            # 初始化审批记录
            await approval_service.init_approval_records(sample_quotation.id, business_type, current_user)
            print("   ✅ 初始化审批记录成功")

            # 提交审批
            await approval_service.submit_for_approval(sample_quotation.id, current_user)
            print("   ✅ 提交审批成功")

            # 更新状态
            await quotation_service.update_project_quotation_status(sample_quotation.id, "1", current_user)
            print("   ✅ 更新状态成功")

            # 验证审批记录
            approval_status = await approval_service.get_approval_status(sample_quotation.id)
            print(f"   ✅ 审批状态: {approval_status.overall_status}")
            print(f"   ✅ 审批记录数: {len(approval_status.approval_records)}")

            # 检查现场审批是否为可选
            field_optional = any(
                record.approver_type == "field" and record.approval_status == "pending"
                for record in approval_status.approval_records
            )
            print(f"   ✅ 现场审批是否可选: {'否（必需）' if field_optional else '是（可选）'}")

            # 清理测试数据
            await session.delete(sampling_quotation)
            await session.delete(sample_quotation)
            await session.delete(test_user)
            await session.commit()
            print("\n✅ 清理测试数据成功")

        await engine.dispose()
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """
    主函数
    """
    print("开始测试更新后的提交审批功能\n")

    if await test_submit_approval_without_business_type():
        print("\n🎉 更新后的提交审批功能测试通过！")
        print("\n✅ 功能验证:")
        print("  • 提交审批时不再需要选择业务类型")
        print("  • 自动从项目报价记录中获取业务类型")
        print("  • 根据业务类型自动确定审批流程:")
        print("    - 一般采样：市场→实验室→现场（三级审批）")
        print("    - 送样：市场→实验室（现场审批可选）")
        print("  • 前端界面已简化，移除业务类型选择对话框")
        print("\n🚀 功能已优化完成，可以使用！")
        return True
    else:
        print("\n❌ 更新后的提交审批功能测试失败")
        return False


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
