"""
项目报价控制器
"""

import io
from typing import Optional

from fastapi import APIRouter, Depends, Path, Query, Request, Response, UploadFile, File
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_customer.service.customer_service import CustomerService
from module_quotation.entity.vo.project_quotation_approver_vo import (
    AddProjectQuotationApproverModel,
)
from module_quotation.entity.vo.project_quotation_vo import (
    AddProjectQuotationModel,
    EditProjectQuotationModel,
    ProjectQuotationModel,
    ProjectQuotationPageQueryModel,
    ProjectQuotationQueryModel,
    ProjectQuotationFeeCalculationModel,
    ProjectQuotationItemImportResultModel,
)
from module_quotation.service.project_quotation_approver_service import ProjectQuotationApproverService
from module_quotation.service.project_quotation_item_import_service import ProjectQuotationItemImportService
from module_quotation.service.project_quotation_service import ProjectQuotationService
from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService
from utils.response_util import ResponseUtil

router = APIRouter(
    prefix="/quotation/project-quotation",
    tags=["项目报价管理"],
)


@router.get("/page", summary="分页查询项目报价")
async def get_project_quotation_page(
    request: Request,
    query_params: ProjectQuotationPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    分页查询项目报价

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 分页查询结果
    """
    # 查询项目报价
    service = ProjectQuotationService(db)
    result = await service.get_project_quotation_page(query_params)

    # 为每个项目添加详细状态
    if result.get("rows"):
        for quotation in result["rows"]:
            current_status = quotation.get("status", "0")
            if current_status == "1":  # 只有待审核状态需要计算详细状态
                detailed_status = await service.get_project_quotation_detailed_status(quotation["id"])
                detailed_status_label = service.get_detailed_status_label(detailed_status)
                quotation["detailedStatus"] = detailed_status
                quotation["detailedStatusLabel"] = detailed_status_label
            else:
                # 对于非待审核状态，detailedStatus应该与status保持一致
                quotation["detailedStatus"] = current_status
                quotation["detailedStatusLabel"] = service.get_status_label(current_status)

    return ResponseUtil.success(data=result)


@router.get("/list", summary="查询项目报价列表")
async def get_project_quotation_list(
    request: Request,
    query_params: ProjectQuotationPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    查询项目报价列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 项目报价列表
    """
    # 查询项目报价
    service = ProjectQuotationService(db)
    result = await service.get_project_quotation_list(query_params)

    return ResponseUtil.success(data=result)


@router.get("/{id}", response_model=ProjectQuotationModel, summary="获取项目报价详情")
async def get_project_quotation(
    request: Request,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取项目报价详情

    :param request: 请求对象
    :param id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 项目报价详情
    """
    service = ProjectQuotationService(db)
    result = await service.get_project_quotation(id)

    return ResponseUtil.success(data=result)


@router.post("", response_model=CrudResponseModel, summary="添加项目报价")
async def add_project_quotation(
    request: Request,
    project_quotation: AddProjectQuotationModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    添加项目报价

    :param request: 请求对象
    :param project_quotation: 项目报价信息
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 添加结果
    """
    service = ProjectQuotationService(db)
    result = await service.add_project_quotation(project_quotation, current_user)

    return ResponseUtil.success(data=result)


@router.put("", response_model=CrudResponseModel, summary="编辑项目报价")
async def edit_project_quotation(
    request: Request,
    project_quotation: EditProjectQuotationModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑项目报价

    :param request: 请求对象
    :param project_quotation: 项目报价信息
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    service = ProjectQuotationService(db)
    result = await service.edit_project_quotation(project_quotation, current_user)

    return ResponseUtil.success(data=result)


@router.put("/quotation-fee", response_model=CrudResponseModel, summary="更新项目报价费用")
async def update_project_quotation_fee(
    request: Request,
    project_quotation: dict,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新项目报价费用

    :param request: 请求对象
    :param project_quotation: 项目报价信息
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 更新结果
    """
    service = ProjectQuotationService(db)
    result = await service.update_project_quotation_fee(project_quotation, current_user)

    return ResponseUtil.success(data=result)


@router.put("/other-fee", response_model=CrudResponseModel, summary="更新单行其他费用")
async def update_single_other_fee(
    request: Request,
    data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新单行其他费用

    :param request: 请求对象
    :param data: 其他费用信息
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 更新结果
    """
    service = ProjectQuotationService(db)
    result = await service.update_single_other_fee(data, current_user)

    return ResponseUtil.success(data=result)


@router.delete("/{id}", response_model=CrudResponseModel, summary="删除项目报价")
async def delete_project_quotation(
    request: Request,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除项目报价

    :param request: 请求对象
    :param id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    service = ProjectQuotationService(db)
    result = await service.delete_project_quotation(id, current_user)

    return ResponseUtil.success(data=result)


@router.get("/export", summary="导出项目报价")
async def export_project_quotation(
    request: Request,
    export_type: str = Query(..., description="导出类型（excel, pdf, word）"),
    project_name: Optional[str] = Query(None, description="项目名称"),
    project_code: Optional[str] = Query(None, description="项目编号"),
    customer_name: Optional[str] = Query(None, description="客户名称"),
    status: Optional[str] = Query(None, description="项目审批状态"),
    begin_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
) -> Response:
    """
    导出项目报价

    :param request: 请求对象
    :param export_type: 导出类型（excel, pdf, word）
    :param project_name: 项目名称
    :param project_code: 项目编号
    :param customer_name: 客户名称
    :param status: 项目审批状态
    :param begin_time: 开始时间
    :param end_time: 结束时间
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 导出文件响应
    """
    # 构建查询参数
    query_params = ProjectQuotationQueryModel(
        project_name=project_name,
        project_code=project_code,
        customer_name=customer_name,
        status=status,
        begin_time=begin_time,
        end_time=end_time,
    )

    # 导出项目报价
    service = ProjectQuotationService(db)
    return await service.export_project_quotation(query_params, export_type)


@router.get("/customer/search", summary="搜索客户")
async def search_customers(
    request: Request,
    keyword: str = Query(..., description="关键字"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    搜索客户

    :param request: 请求对象
    :param keyword: 关键字
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 客户列表
    """
    result = await CustomerService.search_customers_by_name(db, keyword)
    return ResponseUtil.success(data=result)


@router.get("/{id}/fee-calculation", response_model=ProjectQuotationFeeCalculationModel, summary="计算项目报价费用")
async def calculate_project_quotation_fee(
    request: Request,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    计算项目报价费用

    :param request: 请求对象
    :param id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 费用计算结果
    """
    from module_quotation.service.project_quotation_fee_calculation_service import ProjectQuotationFeeCalculationService

    service = ProjectQuotationFeeCalculationService(db)
    await service.sync_basedata_prices(id, current_user)
    result = await service.calculate_project_fees(id)

    return ResponseUtil.success(data=result)


@router.post("/{id}/sync-basedata-prices", response_model=CrudResponseModel, summary="同步基础价目表数据")
async def sync_basedata_prices(
    request: Request,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    同步基础价目表数据

    :param request: 请求对象
    :param id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 同步结果
    """
    from module_quotation.service.project_quotation_fee_calculation_service import ProjectQuotationFeeCalculationService

    service = ProjectQuotationFeeCalculationService(db)
    await service.sync_basedata_prices(id, current_user)

    return ResponseUtil.success(message="基础价目表数据同步成功")


@router.get("/items/import/template", summary="下载项目报价明细批量导入模板")
async def download_import_template(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    下载项目报价明细批量导入模板

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: Excel模板文件
    """
    service = ProjectQuotationItemImportService(db)
    template_data = await service.generate_template()

    return StreamingResponse(
        io.BytesIO(template_data),
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": 'attachment; filename="project_quotation_items_import_template.xlsx"'},
    )


@router.post("/items/import", response_model=ProjectQuotationItemImportResultModel, summary="批量导入项目报价明细")
async def import_project_quotation_items(
    request: Request,
    file: UploadFile = File(..., description="Excel文件"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量导入项目报价明细

    :param request: 请求对象
    :param file: Excel文件
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 导入结果
    """
    # 验证文件类型
    if not file.filename.endswith((".xlsx", ".xls")):
        return ResponseUtil.error(msg="请上传Excel文件（.xlsx或.xls格式）")

    # 验证文件大小（限制为10MB）
    if file.size and file.size > 10 * 1024 * 1024:
        return ResponseUtil.error(msg="文件大小不能超过10MB")

    service = ProjectQuotationItemImportService(db)
    result = await service.import_from_excel(file, current_user)

    return ResponseUtil.success(data=result)


@router.get("/{id}/approvers", summary="获取项目报价审核人列表")
async def get_project_quotation_approvers(
    request: Request,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取项目报价审核人列表

    :param request: 请求对象
    :param id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 审核人列表
    """
    result = await ProjectQuotationApproverService.get_project_quotation_approvers(db, id)
    return ResponseUtil.success(data=result)


@router.post("/{id}/approvers", response_model=CrudResponseModel, summary="设置项目报价审核人")
async def set_project_quotation_approvers(
    request: Request,
    approver_model: AddProjectQuotationApproverModel,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    设置项目报价审核人

    :param request: 请求对象
    :param id: 项目报价ID
    :param approver_model: 审核人信息
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 设置结果
    """
    # 设置项目报价ID
    approver_model.project_quotation_id = id

    result = await ProjectQuotationApproverService.add_project_quotation_approvers(
        db, approver_model, current_user.user_name
    )

    return ResponseUtil.success(data={"success": result})


@router.delete("/{id}/approvers", response_model=CrudResponseModel, summary="删除项目报价审核人")
async def delete_project_quotation_approvers(
    request: Request,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除项目报价审核人

    :param request: 请求对象
    :param id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    result = await ProjectQuotationApproverService.delete_project_quotation_approvers(db, id)

    return ResponseUtil.success(data={"success": result})


@router.post("/{id}/submit-approval", response_model=CrudResponseModel, summary="提交项目报价审批")
async def submit_project_quotation_approval(
    request: Request,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    提交项目报价审批

    :param request: 请求对象
    :param id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 提交结果
    """
    try:
        # 获取项目报价服务
        quotation_service = ProjectQuotationService(db)

        # 检查项目报价是否存在
        quotation = await quotation_service.get_project_quotation(id)
        if not quotation:
            return ResponseUtil.error(msg="项目报价不存在")

        # 检查项目状态是否为草稿
        if quotation.get("status") != "0":
            return ResponseUtil.error(msg="只有草稿状态的项目才能提交审批")

        # 从项目报价中获取业务类型
        business_type = quotation.get("businessType", "sampling")  # 默认为一般采样

        # 获取审批服务
        approval_service = ProjectQuotationApprovalService(db)

        # 根据项目的业务类型初始化审批记录
        await approval_service.init_approval_records(id, business_type, current_user)

        # 提交审批
        await approval_service.submit_for_approval(id, current_user)

        # 更新项目状态为待审核
        await quotation_service.update_project_quotation_status(id, "1", current_user)

        # 根据业务类型返回不同的提示信息
        if business_type == "sample":
            msg = "项目报价已提交审批，进入市场审批阶段（送样项目：市场→实验室审批）"
        else:
            msg = "项目报价已提交审批，进入市场审批阶段（一般采样项目：市场→实验室→现场审批）"

        return ResponseUtil.success(msg=msg)

    except Exception as e:
        return ResponseUtil.error(msg=f"提交审批失败: {str(e)}")


@router.post("/{id}/withdraw-approval", response_model=CrudResponseModel, summary="撤回项目报价审批")
async def withdraw_project_quotation_approval(
    request: Request,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    撤回项目报价审批

    :param request: 请求对象
    :param id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 撤回结果
    """
    try:
        # 获取项目报价服务
        quotation_service = ProjectQuotationService(db)

        # 检查项目报价是否存在
        quotation = await quotation_service.get_project_quotation(id)
        if not quotation:
            return ResponseUtil.error(msg="项目报价不存在")

        # 检查项目状态是否为待审核
        if quotation.get("status") != "1":
            return ResponseUtil.error(msg="只有待审核状态的项目才能撤回")

        # 检查是否为项目创建人或管理员
        is_creator = quotation.get("createBy") == current_user.user.user_name
        is_admin = any(role.role_key == 'admin' for role in current_user.roles)

        # 临时调试信息
        print(f"DEBUG: createBy={quotation.get('createBy')}, current_user={current_user.user.user_name}")
        print(f"DEBUG: is_creator={is_creator}, is_admin={is_admin}")
        print(f"DEBUG: user_roles={[role.role_key for role in current_user.roles]}")

        if not (is_creator or is_admin):
            return ResponseUtil.error(msg="只有项目创建人或管理员才能撤回审批")

        # 获取审批服务
        approval_service = ProjectQuotationApprovalService(db)

        # 撤回审批
        await approval_service.withdraw_approval(id, current_user)

        return ResponseUtil.success(msg="项目审批已撤回，项目状态已恢复为草稿")

    except Exception as e:
        return ResponseUtil.error(msg=f"撤回审批失败: {str(e)}")
