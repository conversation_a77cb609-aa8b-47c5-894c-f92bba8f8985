"""
项目报价审批服务
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import and_, select, func, or_, delete
from sqlalchemy.ext.asyncio import AsyncSession

from config.base_service import BaseService
from exceptions.exception import ServiceException
from module_admin.entity.do.user_do import SysUser, SysUserRole
from module_admin.entity.do.role_do import SysRole
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
from module_quotation.entity.vo.project_quotation_approval_record_vo import (
    ApprovalActionModel,
    ApprovalStatusModel,
    ProjectQuotationApprovalStatusModel,
    AddProjectQuotationApprovalRecordModel
)
from utils.common_util import CamelCaseUtil


class ProjectQuotationApprovalService(BaseService[ProjectQuotationApprovalRecord]):
    """
    项目报价审批服务
    """

    def __init__(self, db: AsyncSession):
        """
        初始化

        :param db: 数据库会话
        """
        super().__init__(ProjectQuotationApprovalRecord, db)
        self.db = db

    async def init_approval_records(self, project_quotation_id: int, business_type: str, current_user: CurrentUserModel):
        """
        初始化审批记录

        :param project_quotation_id: 项目报价ID
        :param business_type: 业务类别
        :param current_user: 当前用户
        """
        # 删除已存在的审批记录
        delete_stmt = select(ProjectQuotationApprovalRecord).where(
            ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id
        )
        existing_records = await self.db.execute(delete_stmt)
        for record in existing_records.scalars().all():
            await self.db.delete(record)

        # 获取各类型审批人员
        market_approvers = await self._get_users_by_role("market-approver")
        lab_approvers = await self._get_users_by_role("lab-approver")
        field_approvers = await self._get_users_by_role("field-approver")

        approval_records = []

        # 市场审批（第一阶段，必需）
        for user in market_approvers:
            record = ProjectQuotationApprovalRecord(
                project_quotation_id=project_quotation_id,
                approver_type="market",
                approver_user_id=user.user_id,
                approval_stage=1,
                is_required="1",
                create_by=current_user.user.user_id,
                create_time=datetime.now()
            )
            approval_records.append(record)

        # 实验室审批（第二阶段，必需）
        for user in lab_approvers:
            record = ProjectQuotationApprovalRecord(
                project_quotation_id=project_quotation_id,
                approver_type="lab",
                approver_user_id=user.user_id,
                approval_stage=2,
                is_required="1",
                create_by=current_user.user.user_id,
                create_time=datetime.now()
            )
            approval_records.append(record)

        # 现场审批（第二阶段，根据业务类型确定是否必需）
        is_field_required = "1" if business_type == "sampling" else "0"
        for user in field_approvers:
            record = ProjectQuotationApprovalRecord(
                project_quotation_id=project_quotation_id,
                approver_type="field",
                approver_user_id=user.user_id,
                approval_stage=2,
                is_required=is_field_required,
                create_by=current_user.user.user_id,
                create_time=datetime.now()
            )
            approval_records.append(record)

        # 批量保存
        for record in approval_records:
            self.db.add(record)
        await self.db.commit()

    async def _get_users_by_role(self, role_key: str) -> List[SysUser]:
        """
        根据角色编码获取用户列表

        :param role_key: 角色编码
        :return: 用户列表
        """
        stmt = (
            select(SysUser)
            .join(SysUserRole, SysUser.user_id == SysUserRole.user_id)
            .join(SysRole, SysUserRole.role_id == SysRole.role_id)
            .where(
                and_(
                    SysRole.role_key == role_key,
                    SysRole.status == "0",  # 角色正常
                    SysUser.status == "0",  # 用户正常
                    SysUser.del_flag == "0"  # 用户未删除
                )
            )
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_approval_status(self, project_quotation_id: int) -> ProjectQuotationApprovalStatusModel:
        """
        获取项目报价审批状态

        :param project_quotation_id: 项目报价ID
        :return: 审批状态
        """
        # 获取项目报价信息
        quotation_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        quotation_result = await self.db.execute(quotation_stmt)
        quotation = quotation_result.scalar_one_or_none()

        if not quotation:
            raise ServiceException(message=f"项目报价不存在：{project_quotation_id}")

        # 获取审批记录
        records_stmt = (
            select(ProjectQuotationApprovalRecord, SysUser.nick_name)
            .join(SysUser, ProjectQuotationApprovalRecord.approver_user_id == SysUser.user_id)
            .where(ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id)
            .order_by(
                ProjectQuotationApprovalRecord.approval_stage,
                ProjectQuotationApprovalRecord.approver_type
            )
        )
        records_result = await self.db.execute(records_stmt)
        records_data = records_result.all()

        approval_records = []
        for record, approver_name in records_data:
            approval_records.append(ApprovalStatusModel(
                approverType=record.approver_type,
                approvalStatus=record.approval_status,
                approverName=approver_name,
                approvalTime=record.approval_time,
                approvalOpinion=record.approval_opinion
            ))

        # 计算整体状态
        overall_status = self._calculate_overall_status(records_data, quotation.business_type)

        return ProjectQuotationApprovalStatusModel(
            projectQuotationId=project_quotation_id,
            businessType=quotation.business_type,
            overallStatus=overall_status,
            approvalRecords=approval_records
        )

    def _calculate_overall_status(self, records_data: List, business_type: str) -> str:
        """
        计算整体审批状态

        :param records_data: 审批记录数据
        :param business_type: 业务类别
        :return: 整体状态
        """
        if not records_data:
            return "0"  # 草稿

        # 提取审批记录（处理元组和对象两种情况）
        records = []
        for item in records_data:
            if isinstance(item, tuple):
                records.append(item[0])  # 元组的第一个元素是记录对象
            else:
                records.append(item)  # 直接是记录对象

        # 检查是否有拒绝的审批
        if any(record.approval_status == "rejected" for record in records):
            return "4"  # 已拒绝

        # 获取必需的审批记录
        required_records = [record for record in records if record.is_required == "1"]

        if not required_records:
            return "0"  # 草稿

        # 检查是否所有必需审批都已通过
        all_approved = all(record.approval_status == "approved" for record in required_records)

        if all_approved:
            return "2"  # 审核完成

        # 检查是否有审批在进行中
        has_pending = any(record.approval_status == "pending" for record in required_records)

        if has_pending:
            return "1"  # 待审核

        return "0"  # 草稿

    def _calculate_detailed_status(self, records_data: List, business_type: str) -> str:
        """
        计算详细的审批状态，包含当前审批阶段信息

        :param records_data: 审批记录数据
        :param business_type: 业务类别
        :return: 详细状态，如 "1-market", "1-lab", "1-field", "1-lab|field"
        """
        if not records_data:
            return "0"  # 草稿

        # 提取审批记录（处理元组和对象两种情况）
        records = []
        for item in records_data:
            if isinstance(item, tuple):
                records.append(item[0])  # 元组的第一个元素是记录对象
            else:
                records.append(item)  # 直接是记录对象

        # 检查是否有拒绝的审批
        if any(record.approval_status == "rejected" for record in records):
            return "4"  # 已拒绝

        # 获取必需的审批记录
        required_records = [record for record in records if record.is_required == "1"]

        if not required_records:
            return "0"  # 草稿

        # 检查是否所有必需审批都已通过
        all_approved = all(record.approval_status == "approved" for record in required_records)

        if all_approved:
            return "2"  # 审核完成

        # 分析当前待审批的阶段
        pending_records = [record for record in required_records if record.approval_status == "pending"]

        if not pending_records:
            return "0"  # 草稿

        # 按阶段分组
        stage1_pending = [r for r in pending_records if r.approval_stage == 1]
        stage2_pending = [r for r in pending_records if r.approval_stage == 2]

        # 如果第一阶段有待审批，返回市场审批
        if stage1_pending:
            return "1-market"

        # 如果第二阶段有待审批，分析具体类型
        if stage2_pending:
            pending_types = [r.approver_type for r in stage2_pending]

            # 检查是否可以进行第二阶段审批（第一阶段必须完成）
            stage1_records = [r for r in required_records if r.approval_stage == 1]
            stage1_completed = all(r.approval_status == "approved" for r in stage1_records)

            if not stage1_completed:
                return "1-market"  # 第一阶段未完成，仍显示市场审批

            # 第二阶段审批分析
            if "lab" in pending_types and "field" in pending_types:
                return "1-lab|field"  # 实验室和现场都需要审批
            elif "lab" in pending_types:
                return "1-lab"  # 只需要实验室审批
            elif "field" in pending_types:
                return "1-field"  # 只需要现场审批

        return "1"  # 默认待审核

    async def submit_for_approval(self, project_quotation_id: int, current_user: CurrentUserModel):
        """
        提交审批

        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        """
        # 更新项目报价状态为待审核
        quotation_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        quotation_result = await self.db.execute(quotation_stmt)
        quotation = quotation_result.scalar_one_or_none()

        if not quotation:
            raise ServiceException(message=f"项目报价不存在：{project_quotation_id}")

        quotation.status = "1"  # 待审核
        quotation.update_by = current_user.user.user_id
        quotation.update_time = datetime.now()

        await self.db.commit()

    async def perform_approval(self, project_quotation_id: int, approval_action: ApprovalActionModel, current_user: CurrentUserModel):
        """
        执行审批操作

        :param project_quotation_id: 项目报价ID
        :param approval_action: 审批操作
        :param current_user: 当前用户
        """
        # 检查当前用户是否有审批权限
        user_approval_record = await self._get_user_approval_record(project_quotation_id, current_user.user.user_id)

        if not user_approval_record:
            raise ServiceException(message="您没有该项目的审批权限")

        if user_approval_record.approval_status != "pending":
            raise ServiceException(message="该审批已处理，无法重复操作")

        # 检查审批阶段是否可以进行
        if not await self._can_approve_at_stage(project_quotation_id, user_approval_record.approval_stage):
            raise ServiceException(message="当前阶段审批条件不满足，无法进行审批")

        # 更新审批记录
        user_approval_record.approval_status = approval_action.approval_status
        user_approval_record.approval_opinion = approval_action.approval_opinion
        user_approval_record.approval_time = datetime.now()
        user_approval_record.update_by = current_user.user.user_id
        user_approval_record.update_time = datetime.now()

        # 更新项目报价整体状态
        await self._update_quotation_status(project_quotation_id, current_user)

        await self.db.commit()

    async def _get_user_approval_record(self, project_quotation_id: int, user_id: int) -> Optional[ProjectQuotationApprovalRecord]:
        """
        获取用户的审批记录

        :param project_quotation_id: 项目报价ID
        :param user_id: 用户ID
        :return: 审批记录
        """
        stmt = select(ProjectQuotationApprovalRecord).where(
            and_(
                ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                ProjectQuotationApprovalRecord.approver_user_id == user_id
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def _can_approve_at_stage(self, project_quotation_id: int, stage: int) -> bool:
        """
        检查是否可以在指定阶段进行审批

        :param project_quotation_id: 项目报价ID
        :param stage: 审批阶段
        :return: 是否可以审批
        """
        if stage == 1:
            # 第一阶段（市场审批）总是可以进行
            return True

        if stage == 2:
            # 第二阶段需要第一阶段的所有必需审批都通过
            stage1_stmt = select(ProjectQuotationApprovalRecord).where(
                and_(
                    ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                    ProjectQuotationApprovalRecord.approval_stage == 1,
                    ProjectQuotationApprovalRecord.is_required == "1"
                )
            )
            stage1_result = await self.db.execute(stage1_stmt)
            stage1_records = stage1_result.scalars().all()

            # 检查第一阶段是否都已通过
            return all(record.approval_status == "approved" for record in stage1_records)

        return False

    async def _update_quotation_status(self, project_quotation_id: int, current_user: CurrentUserModel):
        """
        更新项目报价整体状态

        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        """
        # 获取项目报价
        quotation_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        quotation_result = await self.db.execute(quotation_stmt)
        quotation = quotation_result.scalar_one_or_none()

        # 获取所有审批记录
        records_stmt = select(ProjectQuotationApprovalRecord).where(
            ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id
        )
        records_result = await self.db.execute(records_stmt)
        records = records_result.scalars().all()

        # 计算新状态
        new_status = self._calculate_overall_status([(record, None) for record in records], quotation.business_type)

        # 更新状态
        quotation.status = new_status
        quotation.update_by = current_user.user.user_id
        quotation.update_time = datetime.now()

    async def get_pending_approvals(self, current_user: CurrentUserModel) -> List[Dict[str, Any]]:
        """
        获取当前用户待审批的项目列表

        :param current_user: 当前用户
        :return: 待审批项目列表
        """
        stmt = (
            select(ProjectQuotation, ProjectQuotationApprovalRecord)
            .join(
                ProjectQuotationApprovalRecord,
                ProjectQuotation.id == ProjectQuotationApprovalRecord.project_quotation_id
            )
            .where(
                and_(
                    ProjectQuotationApprovalRecord.approver_user_id == current_user.user.user_id,
                    ProjectQuotationApprovalRecord.approval_status == "pending",
                    ProjectQuotationApprovalRecord.is_required == "1"
                )
            )
            .order_by(ProjectQuotation.create_time.desc())
        )

        result = await self.db.execute(stmt)
        data = result.all()

        pending_list = []
        for quotation, approval_record in data:
            # 检查是否可以审批
            can_approve = await self._can_approve_at_stage(quotation.id, approval_record.approval_stage)

            if can_approve:
                pending_list.append(CamelCaseUtil.transform_result({
                    "id": quotation.id,
                    "project_name": quotation.project_name,
                    "project_code": quotation.project_code,
                    "business_type": quotation.business_type,
                    "customer_name": quotation.customer_name,
                    "approver_type": approval_record.approver_type,
                    "approval_stage": approval_record.approval_stage,
                    "create_time": quotation.create_time,
                    "status": quotation.status
                }))

        return pending_list

    async def withdraw_approval(self, project_quotation_id: int, current_user: CurrentUserModel):
        """
        撤回审批

        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        """
        # 查询项目报价
        quotation_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        quotation_result = await self.db.execute(quotation_stmt)
        quotation = quotation_result.scalar_one_or_none()

        if not quotation:
            raise ServiceException(message=f"项目报价不存在：{project_quotation_id}")

        # 检查项目状态是否为待审核
        if quotation.status != "1":
            raise ServiceException(message="只有待审核状态的项目才能撤回")

        # 检查是否为项目创建人
        if str(quotation.create_by) != str(current_user.user.user_id):
            raise ServiceException(message="只有项目创建人才能撤回审批")

        # 删除所有审批记录
        delete_records_stmt = delete(ProjectQuotationApprovalRecord).where(
            ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id
        )
        await self.db.execute(delete_records_stmt)

        # 更新项目状态为草稿
        quotation.status = "0"  # 草稿
        quotation.update_by = current_user.user.user_id
        quotation.update_time = datetime.now()

        await self.db.commit()

        return True
