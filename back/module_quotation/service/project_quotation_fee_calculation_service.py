"""
项目报价费用计算服务
"""

from collections import defaultdict
from datetime import datetime
from decimal import Decimal
from typing import List, Dict, Any

from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.vo.user_vo import CurrentUserModel
from module_quotation.dao.project_quotation_item_basedata_price_dao import ProjectQuotationItemBasedataPriceDao
from module_quotation.entity.do.project_quotation_item_basedata_price_do import ProjectQuotationItemBasedataPrice
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_quotation.entity.vo.project_quotation_vo import ProjectQuotationFeeCalculationModel
from utils.common_util import CamelCaseUtil


class ProjectQuotationFeeCalculationService:
    """
    项目报价费用计算服务
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def sync_basedata_prices(self, project_quotation_id: int, current_user: CurrentUserModel) -> None:
        """
        同步基础价目表数据

        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        """
        # 1. 获取不重复的(category + method)列表
        unique_category_methods = await ProjectQuotationItemBasedataPriceDao.get_unique_category_method_list(
            self.db, project_quotation_id
        )

        # 2. 删除现有的基础价目表数据
        await ProjectQuotationItemBasedataPriceDao.delete_by_project_quotation_id(self.db, project_quotation_id)

        # 3. 遍历不重复的(category + method)列表，从技术手册价格表获取数据
        basedata_prices = []
        for category, method in unique_category_methods:
            # 从技术手册价格表获取价格信息
            technical_price = await ProjectQuotationItemBasedataPriceDao.get_technical_manual_price_by_category_method(
                self.db, category, method
            )

            if technical_price:
                # 创建基础价目表记录
                basedata_price = ProjectQuotationItemBasedataPrice(
                    project_quotation_id=project_quotation_id,
                    project_quotation_item_id=f"{category}_{method}",  # 使用category_method作为唯一标识
                    method=method,
                    category=category,
                    first_item_price=technical_price.first_item_price,
                    additional_item_price=technical_price.additional_item_price,
                    testing_fee_limit=technical_price.testing_fee_limit,
                    sampling_price=technical_price.sampling_price,
                    pretreatment_price=technical_price.pretreatment_price,
                    # special_consumables_price=technical_price.special_consumables_price,
                    create_by=current_user.user.user_name,
                    create_time=datetime.now(),
                    remark=f"从技术手册价格表同步: {category} - {method}",
                )
                basedata_prices.append(basedata_price)

        # 4. 批量插入基础价目表数据
        if basedata_prices:
            await ProjectQuotationItemBasedataPriceDao.batch_insert(self.db, basedata_prices)
        # 提交事务
        await self.db.commit()

    async def calculate_project_fees(self, project_quotation_id: int) -> ProjectQuotationFeeCalculationModel:
        """
        计算项目费用

        :param project_quotation_id: 项目报价ID
        :return: 费用计算结果
        """
        # 获取项目报价明细
        stmt = select(ProjectQuotationItem).where(
            and_(
                ProjectQuotationItem.project_quotation_id == project_quotation_id,
            )
        )
        result = await self.db.execute(stmt)
        quotation_items = result.scalars().all()

        # 获取基础价目表数据
        basedata_prices = await ProjectQuotationItemBasedataPriceDao.get_by_project_quotation_id(
            self.db, project_quotation_id
        )

        # 创建价格映射字典
        price_map = {}
        for price in basedata_prices:
            key = f"{price.category}_{price.method}"
            price_map[key] = price

        # 计算费用
        total_sampling_fee = Decimal("0")
        total_testing_fee = Decimal("0")
        total_pretreatment_fee = Decimal("0")
        calculation_details = []

        # 按类别-方法分组计算
        category_method_groups = {}
        for item in quotation_items:
            key = f"{item.category}_{item.method}"
            if key not in category_method_groups:
                category_method_groups[key] = []
            category_method_groups[key].append(item)

        for key, items in category_method_groups.items():
            if key not in price_map:
                price_info = ProjectQuotationItemBasedataPrice(
                    first_item_price=Decimal("0"),
                    additional_item_price=Decimal("0"),
                    testing_fee_limit=Decimal("0"),
                    sampling_price=Decimal("0"),
                    pretreatment_price=Decimal("0"),
                )
            else:
                price_info = price_map[key]

            # 计算该类别-方法下的费用
            group_sampling_fee, group_testing_fee, group_pretreatment_fee, group_details = (
                await self._calculate_group_fees(items, price_info)
            )

            total_sampling_fee += group_sampling_fee
            total_testing_fee += group_testing_fee
            total_pretreatment_fee += group_pretreatment_fee
            calculation_details.extend(group_details)

        # 计算总费用
        total_fee = total_sampling_fee + total_testing_fee + total_pretreatment_fee

        return ProjectQuotationFeeCalculationModel(
            sampling_fee=total_sampling_fee,
            testing_fee=total_testing_fee,
            pretreatment_fee=total_pretreatment_fee,
            total_fee=total_fee,
            calculation_details={
                "details": calculation_details,
                "summary": {
                    "sampling_fee": float(total_sampling_fee),
                    "testing_fee": float(total_testing_fee),
                    "pretreatment_fee": float(total_pretreatment_fee),
                    "total_fee": float(total_fee),
                },
            },
        )

    async def _calculate_group_fees(
        self, items: List[ProjectQuotationItem], price_info: ProjectQuotationItemBasedataPrice
    ) -> tuple[Decimal, Decimal, Decimal, List[Dict[str, Any]]]:
        """
        计算同一类别-方法组的费用

        :param items: 项目明细列表，已按类别-方法分组
        :param price_info: 价格信息
        :return: (采样费用, 检测费用, 前处理费用, 计算详情)
        """
        group_sampling_fee = Decimal("0")
        group_testing_fee = Decimal("0")
        group_pretreatment_fee = Decimal("0")
        details = []

        # 根据类别和方法分组
        category_method_groups = defaultdict(list)
        for item in items:
            Key = f"{item.category}_{item.method}"
            category_method_groups[Key].append(item)

        for key, items in category_method_groups.items():
            # 相同的类别和方法，确定采样单价：可合并采样的，多参数仅收取1次采样费用

            # 获取参数数量
            unique_parameters = set(item.parameter for item in items)
            parameter_count = len(unique_parameters)

            # 检测单价计算(来自检测首项单价，检测增项单价，检测费上限），与参数数量有关
            testing_unit_price = self._calculate_testing_unit_price(
                parameter_count,
                price_info.first_item_price,
                price_info.additional_item_price,
                price_info.testing_fee_limit,
            )
            # -------------
            for item in items:

                # 基础计算参数
                point_count = item.point_count or 1
                cycle_count = item.cycle_count or 1
                frequency = item.frequency or 1
                sample_count = item.sample_count or 1

                # 检测费用计算
                testing_fee = testing_unit_price * point_count * cycle_count * frequency * sample_count

                # 采样费用计算(不含采样费用)
                sampling_fee = Decimal("0")
                if item.sample_source == "采样" and price_info.sampling_price:
                    sampling_fee = price_info.sampling_price * point_count * cycle_count * frequency

                # 前处理费用计算
                pretreatment_fee = Decimal("0")
                if price_info.pretreatment_price:
                    pretreatment_fee = price_info.pretreatment_price * point_count * cycle_count * frequency
                group_sampling_fee += sampling_fee
                group_pretreatment_fee += pretreatment_fee
                group_testing_fee += testing_fee

                # 记录计算详情
                details.append(
                    CamelCaseUtil.transform_result(
                        dict(
                            id=item.id,
                            category=item.category,
                            method=item.method,
                            parameter=item.parameter,
                            parameters=unique_parameters,
                            parameter_count=parameter_count,
                            point_name=item.point_name,
                            point_count=point_count,
                            cycle_count=cycle_count,
                            frequency=frequency,
                            sample_count=sample_count,
                            first_item_price=price_info.first_item_price,
                            additional_item_price=price_info.additional_item_price,
                            testing_fee_limit=price_info.testing_fee_limit,
                            testing_unit_price=float(testing_unit_price),
                            sampling_unit_price=float(price_info.sampling_price or 0),
                            pretreatment_unit_price=float(price_info.pretreatment_price or 0),
                            sampling_fee=float(sampling_fee),
                            testing_fee=float(testing_fee),
                            pretreatment_fee=float(pretreatment_fee),
                            item_total_fee=float(sampling_fee + testing_fee + pretreatment_fee),
                        )
                    )
                )
        return group_sampling_fee, group_testing_fee, group_pretreatment_fee, details

    def _calculate_testing_unit_price(
        self,
        parameter_count: int,
        first_item_price: Decimal,
        additional_item_price: Decimal,
        testing_fee_limit: Decimal,
    ) -> Decimal:
        """
        计算检测单价

        :param parameter_count: 参数数量
        :param first_item_price: 首项单价
        :param additional_item_price: 增项单价
        :param testing_fee_limit: 检测费上限
        :return: 检测单价
        """
        if parameter_count <= 0:
            return Decimal("0")

        first_price = first_item_price or Decimal("0")
        additional_price = additional_item_price or Decimal("0")
        fee_limit = testing_fee_limit or Decimal("999999999")

        if parameter_count == 1:
            # 只有一个参数，按首项单价
            unit_price = first_price
        else:
            # 多个参数，按首项单价 + (n-1) * 增项单价
            unit_price = first_price + (parameter_count - 1) * additional_price

        # 不能超过检测费上限
        return min(unit_price, fee_limit)
