"""
项目报价审批记录数据模型
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from config.database import Base


class ProjectQuotationApprovalRecord(Base):
    """
    项目报价审批记录表
    """

    __tablename__ = "project_quotation_approval_record"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    project_quotation_id = Column(Integer, ForeignKey('project_quotation.id'), nullable=False, comment="项目报价ID")
    approver_type = Column(String(20), nullable=False, comment="审批人类型：market-市场审批，lab-实验室审批，field-现场审批")
    approver_user_id = Column(Integer, ForeignKey('sys_user.user_id'), nullable=False, comment="审批人用户ID")
    approval_status = Column(String(10), nullable=False, comment="审批状态：pending-待审批，approved-已通过，rejected-已拒绝")
    approval_opinion = Column(Text, nullable=True, comment="审批意见")
    approval_time = Column(DateTime, nullable=True, comment="审批时间")
    
    # 审批顺序，用于控制审批流程
    approval_order = Column(Integer, nullable=False, comment="审批顺序：1-市场审批，2-实验室/现场审批")
    
    # 创建人和更新人信息
    create_by = Column(Integer, ForeignKey('sys_user.user_id'), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment="创建时间")
    update_by = Column(Integer, ForeignKey('sys_user.user_id'), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, default=datetime.now, comment="更新时间")

    # 关联项目报价表
    project_quotation = relationship("ProjectQuotation", foreign_keys=[project_quotation_id])
    
    # 关联审批人用户表
    approver_user = relationship("SysUser", foreign_keys=[approver_user_id])
    
    # 关联创建人用户表
    creator = relationship("SysUser", foreign_keys=[create_by])
    
    # 关联更新人用户表
    updater = relationship("SysUser", foreign_keys=[update_by])
