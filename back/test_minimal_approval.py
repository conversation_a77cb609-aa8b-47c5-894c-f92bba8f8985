#!/usr/bin/env python3
"""
最小化审批功能测试
"""

import sys
import asyncio
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, '.')

async def test_step_by_step():
    """逐步测试每个组件"""
    print("开始逐步测试...")
    
    # 步骤1：测试基础数据库
    print("\n步骤1：测试数据库基础组件")
    try:
        from config.database import Base
        print("✅ Base 导入成功")
    except Exception as e:
        print(f"❌ Base 导入失败: {e}")
        return False
    
    # 步骤2：测试用户实体
    print("\n步骤2：测试用户实体")
    try:
        from module_admin.entity.do.user_do import SysUser
        user = SysUser(
            user_id=1,
            user_name="test_user",
            nick_name="测试用户",
            password="test_password",
            status="0",
            del_flag="0"
        )
        print("✅ SysUser 创建成功")
    except Exception as e:
        print(f"❌ SysUser 创建失败: {e}")
        return False
    
    # 步骤3：测试角色实体
    print("\n步骤3：测试角色实体")
    try:
        from module_admin.entity.do.role_do import SysRole
        role = SysRole(
            role_id=1,
            role_name="测试角色",
            role_key="test-role",
            role_sort=1,
            status="0"
        )
        print("✅ SysRole 创建成功")
    except Exception as e:
        print(f"❌ SysRole 创建失败: {e}")
        return False
    
    # 步骤4：测试项目报价实体（不导入关联实体）
    print("\n步骤4：测试项目报价实体")
    try:
        # 先单独导入项目报价
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        quotation = ProjectQuotation(
            id=1,
            project_name="测试项目",
            project_code="TEST001",
            business_type="sampling",
            status="0",
            customer_name="测试客户"
        )
        print("✅ ProjectQuotation 创建成功")
    except Exception as e:
        print(f"❌ ProjectQuotation 创建失败: {e}")
        return False
    
    # 步骤5：测试审批记录实体
    print("\n步骤5：测试审批记录实体")
    try:
        from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
        approval_record = ProjectQuotationApprovalRecord(
            project_quotation_id=1,
            approver_type="market",
            approver_user_id=1,
            approval_stage=1,
            is_required="1",
            approval_status="pending"
        )
        print("✅ ProjectQuotationApprovalRecord 创建成功")
    except Exception as e:
        print(f"❌ ProjectQuotationApprovalRecord 创建失败: {e}")
        return False
    
    # 步骤6：测试VO模型
    print("\n步骤6：测试VO模型")
    try:
        from module_admin.entity.vo.user_vo import UserInfoModel, CurrentUserModel
        user_info = UserInfoModel(
            user_id=1,
            user_name="test_user",
            nick_name="测试用户",
            status="0",
            del_flag="0"
        )
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=user_info
        )
        print("✅ UserInfoModel 和 CurrentUserModel 创建成功")
    except Exception as e:
        print(f"❌ VO模型创建失败: {e}")
        return False
    
    # 步骤7：测试审批操作模型
    print("\n步骤7：测试审批操作模型")
    try:
        from module_quotation.entity.vo.project_quotation_approval_record_vo import ApprovalActionModel
        approval_action = ApprovalActionModel(
            projectQuotationId=1,
            approvalStatus="approved",
            approvalOpinion="测试审批通过"
        )
        print("✅ ApprovalActionModel 创建成功")
    except Exception as e:
        print(f"❌ ApprovalActionModel 创建失败: {e}")
        return False
    
    # 步骤8：测试DAO
    print("\n步骤8：测试DAO")
    try:
        from module_quotation.dao.project_quotation_approval_record_dao import ProjectQuotationApprovalRecordDao
        print("✅ ProjectQuotationApprovalRecordDao 导入成功")
    except Exception as e:
        print(f"❌ ProjectQuotationApprovalRecordDao 导入失败: {e}")
        return False
    
    # 步骤9：测试服务
    print("\n步骤9：测试服务")
    try:
        from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService
        print("✅ ProjectQuotationApprovalService 导入成功")
    except Exception as e:
        print(f"❌ ProjectQuotationApprovalService 导入失败: {e}")
        return False
    
    print("\n🎉 所有步骤测试都通过了！")
    return True


async def test_database_operations():
    """测试数据库操作"""
    print("\n开始数据库操作测试...")
    
    try:
        from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
        from sqlalchemy.orm import sessionmaker
        from config.database import Base
        from module_admin.entity.do.user_do import SysUser
        from module_admin.entity.do.role_do import SysRole
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
        
        # 创建测试数据库引擎
        TEST_DATABASE_URL = "sqlite+aiosqlite:///./test_minimal.db"
        engine = create_async_engine(TEST_DATABASE_URL, echo=False)
        TestSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
        
        # 创建表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("✅ 数据库表创建成功")
        
        # 创建会话并添加数据
        async with TestSessionLocal() as session:
            # 创建角色
            role = SysRole(
                role_id=1,
                role_name="市场审批人员",
                role_key="market-approver",
                role_sort=1,
                status="0",
                create_by="admin",
                create_time=datetime.now()
            )
            session.add(role)
            await session.flush()
            
            # 创建用户
            user = SysUser(
                user_id=1,
                user_name="test_user",
                nick_name="测试用户",
                password="test_password",
                status="0",
                del_flag="0",
                create_by="admin",
                create_time=datetime.now()
            )
            session.add(user)
            await session.flush()
            
            # 创建项目报价
            quotation = ProjectQuotation(
                id=1,
                project_name="测试项目",
                project_code="TEST001",
                business_type="sampling",
                status="0",
                customer_name="测试客户",
                create_by=user.user_id,
                create_time=datetime.now()
            )
            session.add(quotation)
            await session.flush()
            
            # 创建审批记录
            approval_record = ProjectQuotationApprovalRecord(
                project_quotation_id=quotation.id,
                approver_type="market",
                approver_user_id=user.user_id,
                approval_stage=1,
                is_required="1",
                approval_status="pending",
                create_by=user.user_id,
                create_time=datetime.now()
            )
            session.add(approval_record)
            await session.flush()
            
            await session.commit()
            print("✅ 数据库操作成功")
        
        # 清理表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        print("✅ 数据库清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        return False


async def main():
    """主函数"""
    print("开始最小化审批功能测试\n")
    
    # 逐步测试
    if not await test_step_by_step():
        print("\n❌ 逐步测试失败")
        return False
    
    # 数据库操作测试
    if not await test_database_operations():
        print("\n❌ 数据库操作测试失败")
        return False
    
    print("\n🎉 所有测试都通过了！")
    return True


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
