#!/usr/bin/env python3
"""
基本审批功能测试
"""

import sys
import asyncio
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, '.')

async def test_basic_imports():
    """测试基本导入"""
    print("测试基本模块导入...")
    
    try:
        from config.database import Base
        print("✅ config.database.Base 导入成功")
    except ImportError as e:
        print(f"❌ config.database.Base 导入失败: {e}")
        return False
    
    try:
        from module_admin.entity.do.user_do import SysUser, SysUserRole
        print("✅ SysUser, SysUserRole 导入成功")
    except ImportError as e:
        print(f"❌ SysUser, SysUserRole 导入失败: {e}")
        return False
    
    try:
        from module_admin.entity.do.role_do import SysRole
        print("✅ SysRole 导入成功")
    except ImportError as e:
        print(f"❌ SysRole 导入失败: {e}")
        return False
    
    try:
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        print("✅ ProjectQuotation 导入成功")
    except ImportError as e:
        print(f"❌ ProjectQuotation 导入失败: {e}")
        return False
    
    try:
        from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
        print("✅ ProjectQuotationApprovalRecord 导入成功")
    except ImportError as e:
        print(f"❌ ProjectQuotationApprovalRecord 导入失败: {e}")
        return False
    
    try:
        from module_quotation.dao.project_quotation_approval_record_dao import ProjectQuotationApprovalRecordDao
        print("✅ ProjectQuotationApprovalRecordDao 导入成功")
    except ImportError as e:
        print(f"❌ ProjectQuotationApprovalRecordDao 导入失败: {e}")
        return False
    
    try:
        from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService
        print("✅ ProjectQuotationApprovalService 导入成功")
    except ImportError as e:
        print(f"❌ ProjectQuotationApprovalService 导入失败: {e}")
        return False
    
    return True


async def test_entity_creation():
    """测试实体创建"""
    print("\n测试实体创建...")
    
    try:
        from module_admin.entity.do.user_do import SysUser
        from module_admin.entity.do.role_do import SysRole
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
        
        # 创建角色
        role = SysRole(
            role_id=1,
            role_name="测试角色",
            role_key="test-role",
            role_sort=1,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        print("✅ SysRole 实体创建成功")
        
        # 创建用户
        user = SysUser(
            user_id=1,
            user_name="test_user",
            nick_name="测试用户",
            password="test_password",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        print("✅ SysUser 实体创建成功")
        
        # 创建项目报价
        quotation = ProjectQuotation(
            id=1,
            project_name="测试项目",
            project_code="TEST001",
            business_type="sampling",
            status="0",
            customer_name="测试客户",
            create_by=user.user_id,
            create_time=datetime.now()
        )
        print("✅ ProjectQuotation 实体创建成功")
        
        # 创建审批记录
        approval_record = ProjectQuotationApprovalRecord(
            project_quotation_id=quotation.id,
            approver_type="market",
            approver_user_id=user.user_id,
            approval_stage=1,
            is_required="1",
            approval_status="pending",
            create_by=user.user_id,
            create_time=datetime.now()
        )
        print("✅ ProjectQuotationApprovalRecord 实体创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 实体创建失败: {e}")
        return False


async def test_vo_models():
    """测试VO模型"""
    print("\n测试VO模型...")
    
    try:
        from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
        from module_quotation.entity.vo.project_quotation_approval_record_vo import ApprovalActionModel
        
        # 创建用户信息模型
        user_info = UserInfoModel(
            user_id=1,
            user_name="test_user",
            nick_name="测试用户",
            status="0",
            del_flag="0"
        )
        print("✅ UserInfoModel 创建成功")
        
        # 创建当前用户模型
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=user_info
        )
        print("✅ CurrentUserModel 创建成功")
        
        # 创建审批操作模型
        approval_action = ApprovalActionModel(
            project_quotation_id=1,
            approval_status="approved",
            approval_opinion="测试审批通过"
        )
        print("✅ ApprovalActionModel 创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ VO模型创建失败: {e}")
        return False


async def test_database_connection():
    """测试数据库连接"""
    print("\n测试数据库连接...")
    
    try:
        from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
        from sqlalchemy.orm import sessionmaker
        from config.database import Base
        
        # 创建测试数据库引擎
        TEST_DATABASE_URL = "sqlite+aiosqlite:///./test_approval.db"
        engine = create_async_engine(TEST_DATABASE_URL, echo=False)
        TestSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
        
        # 创建表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("✅ 数据库表创建成功")
        
        # 创建会话
        async with TestSessionLocal() as session:
            print("✅ 数据库会话创建成功")
        
        # 清理表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        print("✅ 数据库表清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("开始基本审批功能测试\n")
    
    # 测试基本导入
    if not await test_basic_imports():
        print("\n❌ 基本导入测试失败")
        return False
    
    # 测试实体创建
    if not await test_entity_creation():
        print("\n❌ 实体创建测试失败")
        return False
    
    # 测试VO模型
    if not await test_vo_models():
        print("\n❌ VO模型测试失败")
        return False
    
    # 测试数据库连接
    if not await test_database_connection():
        print("\n❌ 数据库连接测试失败")
        return False
    
    print("\n🎉 所有基本测试都通过了！")
    return True


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
